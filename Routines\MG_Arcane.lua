  function A_62(...)
    -- HR UPDATE: fix(Arcane): Attempt to fix AM interrupts 01/06/2025
    -- REMEMBER: S.ShiftingPower:IsCastable(true) / ShouldUseShiftingPower = true
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastCycle = M.CastCycle
    local AoEON = M.AoEON
    local num = M.num
    -- LUAs
    local IsInGroup = _G['IsInGroup']
    local IsInRaid = _G['IsInRaid']
    local mathmin     = math.min
    local mathmax       = math.max
    local UnitPower = _G['UnitPower']
    local Enum = _G['Enum']

    -- Define S/I for spell and item arrays
    local S = Spell.Mage.Arcane
    local I = Item.Mage.Arcane

    local OnUseExcludes = {
      -- TWW Trinkets
      I.AberrantSpellforge:ID(),
      I.FearbreakersEcho:ID(),
      I.HighSpeakersAccretion:ID(),
      I.ImperfectAscendancySerum:ID(),
      I.MadQueensMandate:ID(),
      I.MereldarsToll:ID(),
      I.NeuralSynapseEnhancer:ID(),
      I.SpymastersWeb:ID(),
      I.TreacherousTransmitter:ID(),
    }

    MainAddon.Toggle.Special["ForceOpener"] = {
        Icon = MainAddon.GetTexture(S.Evocation),
        Name = "Force Opener",
        Description = "This toggle will force Opener sequence.",
        Spec = 62,
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = "3FC7EB"
    local Config_Table = {
        key = Config_Key,
        title = "Mage - Arcane",
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            {type = "header", text = "", size = 24, align = "Center", color = Config_Color},
            { type = 'header', text = '', size = 18, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },    
            { type = "header", text = '\"There is no prize to perfection, only an end to pursuit.\"', size = 16, align = "center", color = Config_Color },
            { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },   
            { type = 'spacer' },
            { type = 'header', text = 'APL', color = Config_Color }, 
            {
              type = 'dropdown',
              text = ' Rotation Mode',
              icon = S.Arcanosphere:ID(),
              key = 'rotation_mode',
              list = {
                  { text = 'SimC APL', key = 'APLsimc' },
                  { text = 'Custom Rotation', key = 'APLcustom' }
              },
              default = 'APLcustom'
            },    
            { type = 'spacer' },
            {type = "spacer"},
            {type = "ruler"},
            {type = "spacer"},
            {type = "header", text = "DPS", color = Config_Color},
            { type = 'checkbox', text = ' Disable dispel and low priority kicks while under Arcane Surge', icon = S.ArcaneSurge:ID(), key = 'arcanesurge_nodispel', default = true },
            {
                type = "checkbox",
                text = " Mirror Image prepull",
                icon = S.MirrorImage:ID(),
                key = "mirrorimage_prepull",
                default = true
            },
            {
                type = "checkbox",
                text = " Evocation prepull",
                icon = S.Evocation:ID(),
                key = "evocation_prepull",
                default = true
            },
            { 
                type = 'dropdown',
                text = " Re-Target Touch of the Magi's unit", key = 'retarget_TOTM',
                icon = S.TouchoftheMagi:ID(),
                list = {
                  { text = 'MouseOver', key = 1 },
                  { text = 'Auto', key = 2 },
                  { text = 'None', key = 3 },
                },
                default = 1,
            },
            {
                type = "spinner",
                text = " Shifting Power - Stand still threshold",
                key = "SPMovingValue",
                icon = S.ShiftingPower:ID(),
                min = 0,
                max = 10,
                default = 1.5
            },
            {
                type = "checkbox",
                text = " Arcane Orb - When player is turning",
                icon = S.ArcaneOrb:ID(),
                key = "arcaneorb_when_turning",
                default = true
            },
            {type = "spacer"},
            {type = "header", text = "Defensives", color = Config_Color},
            {
                type = "checkspin",
                text = " Prismatic Barrier",
                key = "PB",
                icon = S.PrismaticBarrier:ID(),
                min = 1,
                max = 100,
                default_spin = 55,
                default_check = true
            },
            { type = 'checkspin', text = ' Mass Barrier - Average Group health threshold', key = 'MassBB', icon = S.MassBarrier:ID(), min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'checkspin', text = ' Mirror Image - Defensive', key = 'mi_defensive', icon = S.MirrorImage:ID(), min = 1, max = 100, default_spin = 40, default_check = false },
            { type = 'checkspin', text = ' Ice Cold / Ice Block', key = 'ICIB', icon = S.IceBlock:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
            { type = "spacer" },
            { type = "header", text = "Utilities", color = Config_Color },
            { type = 'checkbox', text = ' Mirror Image - Aggro', icon = S.MirrorImage:ID(), key = 'mi_aggro', default = true },
            { type = 'checkbox', text = ' Greater Invisibility - Aggro', icon = S.GreaterInvisibility:ID(), key = 'gi_aggro', default = true },
            { type = 'dropdown',
              text = ' Ice Floes', key = 'if',
              multiselect = true,
              icon = S.IceFloes:ID(),
              list = {
                  { text = 'Shifting Power', key = 1 },
                  { text = 'Evocation', key = 2 },
                  { text = 'Arcane Surge', key = 3 },
              },
              default = {
                  1,
                  2,
              },
            },
            {
                type = "dropdown",
                text = " Arcane Intellect",
                key = "int",
                icon = S.ArcaneIntellect:ID(),
                multiselect = true,
                list = {
                    {text = "Self", key = "int_self"},
                    {text = "Friends", key = "int_friends"}
                },
                default = {"int_self", "int_friends"}
            },
            { type = 'checkbox', text = ' Toast message about Shifting Power', icon = S.ShiftingPower:ID(), key = 'toast_SP', default = true },
            { type = 'spinner', text = ' Shifting Power Toast message reset timer (sec)', icon = S.ShiftingPower:ID(), key = 'toast_SP_reset', min = 0, max = 60, default = 15 },
            { type = "spacer" },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Arcane", Config_Color)
    M.SetConfig(62, Config_Table)

    --- ===== Start Custom =====
    local Enemies40y = {}
    local EnemiesCount40y = 0
    local ArcaneCharges = 0
    local ShouldReturn
    local ShouldUseShiftingPower = false
    --- ===== Stop Custom =====

    --- --- ===== Helper Functions =====
    -- Safely checks tier set bonuses while handling missing or invalid tier functions
    local function HasTierBonus(tier, pieces)
      return Player:HasTier("TWW" .. tier, pieces)
    end

    --- ===== InFlight Registrations =====
    HL:RegisterForEvent(
      function()
          S.ArcaneBlast:RegisterInFlight()
          S.ArcaneBarrage:RegisterInFlight()
      end,
    "SPELLS_CHANGED","LEARNED_SPELL_IN_TAB")
    S.ArcaneBlast:RegisterInFlight()
    S.ArcaneBarrage:RegisterInFlight()

    --- ===== Rotation Variables =====
    local VarAoETargetCount = (not S.ArcingCleave:IsAvailable()) and 9 or 2
    local VarOpener = true
    local VarAoEList = false
    local Enemies8ySplash, EnemiesCount8ySplash
    local ClearCastingMaxStack = S.ImprovedClearcasting:IsAvailable() and 3 or 1
    local LastSSAM = 0
    local LastSFAM = 0
    local BossFightRemains = 11111
    local FightRemains = 11111

    --- ===== Trinket Variables =====
    local VarTreacherousTransmitterPrecombatCast = 11
    local VarSteroidTrinketEquipped = Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.SpymastersWeb:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped() or I.NeuralSynapseEnhancer:IsEquipped()
    local VarNeuralOnMini = Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.SpymastersWeb:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped()
    -- Variable to track equipped nonsteroid trinkets from SimC: blastmaster3000|ratfang_toxin|ingenious_mana_battery|geargrinders_spare_keys|ringing_ritual_mud|goo_blin_grenade|noggenfogger_ultimate_deluxe|garbagemancers_last_resort|mad_queens_mandate|fearbreakers_echo|mereldars_toll|gooblin_grenade
    local VarNonsteroidTrinketEquipped = I.FearbreakersEcho:IsEquipped() or I.MadQueensMandate:IsEquipped() or I.MereldarsToll:IsEquipped() or I.AberrantSpellforge:IsEquipped()
    local VarSpymastersDoubleOnUse = (Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped()) and I.SpymastersWeb:IsEquipped() and S.Evocation:CooldownRemains() < 17 and (Player:BuffStack(S.SpymastersReportBuff) > 35 or (FightRemains < 90 and Player:BuffStack(S.SpymastersReportBuff) > 25))

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
      VarAoETargetCount = (not S.ArcingCleave:IsAvailable()) and 9 or 2
      VarOpener = true
      VarAoEList = false
      LastSSAM = 0
      LastSFAM = 0
      BossFightRemains = 11111
      FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    HL:RegisterForEvent(function()
      VarAoETargetCount = (not S.ArcingCleave:IsAvailable()) and 9 or 2
      ClearCastingMaxStack = S.ImprovedClearcasting:IsAvailable() and 3 or 1
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    HL:RegisterForEvent(function()
      VarSteroidTrinketEquipped = Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.SpymastersWeb:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped() or I.NeuralSynapseEnhancer:IsEquipped()
      VarNeuralOnMini = Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.SpymastersWeb:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped()
      VarNonsteroidTrinketEquipped = I.FearbreakersEcho:IsEquipped() or I.MadQueensMandate:IsEquipped() or I.MereldarsToll:IsEquipped() or I.AberrantSpellforge:IsEquipped()
      VarSpymastersDoubleOnUse = (Player:GladiatorsBadgeIsEquipped() or I.SignetofthePriory:IsEquipped() or I.HighSpeakersAccretion:IsEquipped() or I.TreacherousTransmitter:IsEquipped() or I.ImperfectAscendancySerum:IsEquipped() or I.QuickwickCandlestick:IsEquipped()) and I.SpymastersWeb:IsEquipped() and S.Evocation:CooldownRemains() < 17 and (Player:BuffStack(S.SpymastersReportBuff) > 35 or (FightRemains < 90 and Player:BuffStack(S.SpymastersReportBuff) > 25))
    end, "PLAYER_EQUIPMENT_CHANGED")

    --- ===== Start Custom =====
    local function UnitWithTOTM(enemies)
      if Target:DebuffUp(S.TouchoftheMagiDebuff) then
          return 0
      end
      local Count = 0
      for k in pairs(enemies) do
          ---@class Unit
          local CycleUnit = enemies[k]
          if CycleUnit:DebuffUp(S.TouchoftheMagiDebuff) then
              Count = Count + 1
          end
      end
        return Count
    end

    local function Defensives()
        local DefensiveUp = Player:BuffUp(S.PrismaticBarrier) or Player:BuffUp(S.IceCold) or Player:BuffUp(S.MirrorImageBuff) or Player:BuffUp(S.IceBlock)
        if not DefensiveUp then
            if Player:BuffDown(S.PrismaticBarrier) then
                if GetSetting("PB_check", false) then
                    if S.PrismaticBarrier:IsReady(Player) and Player:HealthPercentage() <= GetSetting("PB_spin", 30) then
                        if Cast(S.PrismaticBarrier) then
                            return "Prismatic Barrier"
                        end
                    end
                end
            end

            if Player:AffectingCombat() then  
                if S.MirrorImage:IsReady(Player) and GetSetting('mi_defensive_check', false) and Player:HealthPercentage() <= GetSetting('mi_defensive_spin', 40) then
                    if Cast(S.MirrorImage) then
                        return "Mirror Image - HP"
                    end
                end

                if GetSetting('ICIB_check', false) and Player:HealthPercentage() <= GetSetting('ICIB_spin', 35) and Player:DebuffDown(S.HypothermiaDebuff) then
                    if S.IceCold:IsReady(Player) then
                        if Cast(S.IceCold) then
                            return "Ice Cold - HP"
                        end
                    end
    
                    if S.IceBlock:IsReady(Player) then
                        if Cast(S.IceBlock) then
                            return "Ice Block - HP"
                        end
                    end
                end
            end
        end

        if Player:AffectingCombat() then  
            if Player:IsInDungeonArea() or Player:IsInRaidArea() then
                if S.MassBarrier:IsReady(Player) and GetSetting('MassBB_check', false) and MainAddon.HealingEngine:MedianHP() <= GetSetting('MassBB_spin', 30) then
                    if Cast(S.MassBarrier) then
                        return "Mass Barrier"
                    end
                end
            end
        end
    end

    local function Utilities()
        -- arcane_intellect
        local int = GetSetting("int", {})
        if S.ArcaneIntellect:IsReady(Player) 
        and (int["int_self"] and Player:BuffDown(S.ArcaneIntellect, true) 
        or int["int_friends"] and M.GroupBuffMissing(S.ArcaneIntellect)) then
            if Cast(S.ArcaneIntellect) then
                return "arcane_intellect precombat 2"
            end
        end

        if Player:IsTankingAoE(40) then
            if GetSetting('gi_aggro', true) then
                if S.GreaterInvisibility:IsReady(Player) and not Player:PrevGCD(1, S.MirrorImage) and (IsInGroup() or IsInRaid()) then
                    if Cast(S.GreaterInvisibility) then
                        return 'Greater Invisibility'
                    end
                end
            end
            if GetSetting('mi_aggro', true) then
                if S.MirrorImage:IsReady(Player) and not Player:PrevGCD(1, S.GreaterInvisibility) and Player:BuffDown(S.GreaterInvisibilityBuff) then
                    if Cast(S.MirrorImage) then
                        return 'Mirror Image'
                    end
                end
            end
        end
    end

    local function Opener()
      if (not S.MirrorImage:IsReady() or not GetSetting("mirrorimage_prepull", false)) and (not S.Evocation:IsReady() or not GetSetting("evocation_prepull", false)) and (Player:IsCasting(S.ArcaneBlast) or Player:AffectingCombat() and ArcaneCharges >= 4) then
          MainAddon.Toggle:SetToggle("ForceOpener", false)
          return
      end
        -- mirror_image
        if GetSetting("mirrorimage_prepull", false) and S.MirrorImage:IsReady() then
            if Cast(S.MirrorImage) then return "mirror_image Opener"; end
        end
        -- evocation,if=talent.siphon_storm
        if GetSetting("evocation_prepull", false) and S.Evocation:IsReady(Player) then
            if Cast(S.Evocation) then return "evocation Opener"; end
        end
        if M.TargetIsValid() then
            -- arcane_blast,if=!talent.siphon_storm
            if S.ArcaneBlast:IsReady() then
                if Cast(S.ArcaneBlast) then return "arcane_blast Opener"; end
            end
        end
    end
    --- ===== End Custom =====

    --- ===== Rotation Functions =====
    local function Precombat()
        -- flask
        -- food
        -- augmentation
        -- arcane_intellect
        -- Note: Moved to top of APL()
        -- variable,name=aoe_target_count,op=reset,default=2
        -- variable,name=aoe_target_count,op=set,value=9,if=!talent.arcing_cleave
        -- variable,name=opener,op=set,value=1
        -- variable,name=sunfury_aoe_list,default=0,op=reset
        -- Note: Moved to variable declarations and Event Registrations to avoid potential nil errors.
        -- variable,name=steroid_trinket_equipped,op=set,value=equipped.gladiators_badge|equipped.signet_of_the_priory|equipped.high_speakers_accretion|equipped.spymasters_web|equipped.treacherous_transmitter|equipped.imperfect_ascendancy_serum
        -- Note: Moved to SetTrinketVariables().
        -- snapshot_stats
        -- mirror_image
        if M.CombinedPullTimer() < 7 and MainAddon.TargetIsValid() then
            if GetSetting("mirrorimage_prepull", false) and S.MirrorImage:IsReady(Player) then
                if Cast(S.MirrorImage) then return "mirror_image precombat 6"; end
            end
            -- evocation,if=talent.siphon_storm
            if GetSetting("evocation_prepull", false) and S.Evocation:IsReady(Player) then
                if Cast(S.Evocation) then return "evocation precombat 10"; end
            end
            -- arcane_blast,if=!talent.siphon_storm
            if S.ArcaneBlast:IsReady() then
                if Cast(S.ArcaneBlast) then return "arcane_blast precombat 8"; end
            end
        end
    end

    local function CDOpener()
      -- touch_of_the_magi,use_off_gcd=1,if=prev_gcd.1.arcane_barrage&(action.arcane_barrage.in_flight_remains<=0.5|gcd.remains<=0.5)&(buff.arcane_surge.up|cooldown.arcane_surge.remains>30)|(prev_gcd.1.arcane_surge&(buff.arcane_charge.stack<4|buff.nether_precision.down))|(cooldown.arcane_surge.remains>30&cooldown.touch_of_the_magi.ready&buff.arcane_charge.stack<4&!prev_gcd.1.arcane_barrage)
      -- Note: Added an extra half second buffer time.
      if S.TouchoftheMagi:IsReady() and (Player:PrevGCDP(1, S.ArcaneBarrage) and (S.ArcaneBarrage:TravelTime() - S.ArcaneBarrage:TimeSinceLastCast() <= 1 or Player:GCDRemains() <= 1) and (Player:BuffUp(S.ArcaneSurgeBuff) or S.ArcaneSurge:CooldownRemains() > 30) or (Player:PrevGCDP(1, S.ArcaneSurge) and (Player:ArcaneCharges() < 4 or Player:BuffDown(S.NetherPrecisionBuff))) or (S.ArcaneSurge:CooldownRemains() > 30 and S.TouchoftheMagi:CooldownUp() and Player:ArcaneCharges() < 4 and not Player:PrevGCDP(1, S.ArcaneBarrage))) then
        if Cast(S.TouchoftheMagi) then return "touch_of_the_magi cd_opener 2"; end
      end
      -- wait,sec=0.05,if=prev_gcd.1.arcane_surge&time-action.touch_of_the_magi.last_used<0.015,line_cd=15
      -- arcane_blast,if=buff.presence_of_mind.up
      if S.ArcaneBlast:IsReady() and (Player:BuffUp(S.PresenceofMindBuff)) then
        if Cast(S.ArcaneBlast) then return "arcane_blast cd_opener 4"; end
      end
      -- arcane_orb,if=talent.high_voltage&variable.opener,line_cd=10
      if S.ArcaneOrb:IsReady() and S.ArcaneOrb:TimeSinceLastCast() >= 10 and (S.HighVoltage:IsAvailable() and VarOpener) then
        if Cast(S.ArcaneOrb) then return "arcane_orb cd_opener 6"; end
      end
      -- arcane_barrage,if=buff.arcane_tempo.up&cooldown.evocation.ready&buff.arcane_tempo.remains<gcd.max*5,line_cd=11
      if S.ArcaneBarrage:IsReady() and S.ArcaneBarrage:TimeSinceLastCast() >= 11 and (Player:BuffUp(S.ArcaneTempoBuff) and S.Evocation:CooldownUp() and Player:BuffRemains(S.ArcaneTempoBuff) < Player:GCD() * 5) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage cd_opener 7"; end
      end
      -- evocation,if=cooldown.arcane_surge.remains<(gcd.max*3)&cooldown.touch_of_the_magi.remains<(gcd.max*5)
      if S.Evocation:IsReady() and (S.ArcaneSurge:CooldownRemains() < (Player:GCD() * 3) and S.TouchoftheMagi:CooldownRemains() < (Player:GCD() * 5)) then
        if Cast(S.Evocation) then return "evocation cd_opener 8"; end
      end
      -- -- arcane_missiles,if=((prev_gcd.1.evocation|prev_gcd.1.arcane_surge)|variable.opener)&buff.nether_precision.down&(buff.aether_attunement.react=0|set_bonus.thewarwithin_season_2_4pc),interrupt_if=tick_time>gcd.remains&(buff.aether_attunement.react=0|(active_enemies>3&(!talent.time_loop|talent.resonance))),interrupt_immediate=1,interrupt_global=1,chain=1,line_cd=30
      -- if Player:IsChanneling(S.ArcaneMissiles) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and (Player:BuffDown(S.AetherAttunementBuff) or (EnemiesCount8ySplash > 3 and (not S.TimeLoop:IsAvailable() or S.Resonance:IsAvailable())))) then
      --   if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt cd_opener 10"; end
      -- end
      -- clipping logic
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
          if MainAddon.SetTopColor(1, "Stop Casting") then
              return "arcane_missiles interrupt cd_opener"
          end
      end     
      if S.ArcaneMissiles:IsReady() and S.ArcaneMissiles:TimeSinceLastCast() >= 30 and (((Player:PrevGCDP(1, S.Evocation) or Player:PrevGCDP(1, S.ArcaneSurge)) or VarOpener) and Player:BuffDown(S.NetherPrecisionBuff) and (Player:BuffStack(S.AetherAttunementBuff) == 0 or HasTierBonus(2, 4))) then
        if Cast(S.ArcaneMissiles) then return "arcane_missiles cd_opener 12"; end
      end
      -- arcane_surge;custom
      if GetSetting("rotation_mode") == "APLcustom" and S.ArcaneSurge:IsReady() and (S.TouchoftheMagi:CooldownRemains() < 1.95) then
        if Cast(S.ArcaneSurge) then return "Entering Surge Window"; end
      end
      -- arcane_surge,if=cooldown.touch_of_the_magi.remains<(action.arcane_surge.execute_time+(gcd.max*(buff.arcane_charge.stack=4)))
      if GetSetting("rotation_mode") == "APLsimc" and S.ArcaneSurge:IsReady() and (S.TouchoftheMagi:CooldownRemains() < (S.ArcaneSurge:ExecuteTime() + (Player:GCD() * num(Player:ArcaneCharges() == 4)))) then
        if Cast(S.ArcaneSurge) then return "arcane_surge cd_opener 14"; end
      end
    end

    local function Spellslinger()
      -- Note: Handle arcane_missiles interrupts.
      if Player:IsChanneling(S.ArcaneMissiles) and (LastSSAM == 1 or LastSSAM == 2) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and (Player:BuffDown(S.AetherAttunementBuff) or (EnemiesCount8ySplash > 3 and (not S.TimeLoop:IsAvailable() or S.Resonance:IsAvailable())))) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt spellslinger 2"; end
      end
      -- shifting_power,if=(((((action.arcane_orb.charges=talent.charged_orb)&cooldown.arcane_orb.remains)|cooldown.touch_of_the_magi.remains<23)&buff.arcane_surge.down&buff.siphon_storm.down&debuff.touch_of_the_magi.down&(buff.intuition.react=0|(buff.intuition.react&buff.intuition.remains>cast_time))&cooldown.touch_of_the_magi.remains>(12+6*gcd.max))|(prev_gcd.1.arcane_barrage&talent.shifting_shards&(buff.intuition.react=0|(buff.intuition.react&buff.intuition.remains>cast_time))&(buff.arcane_surge.up|debuff.touch_of_the_magi.up|cooldown.evocation.remains<20)))&fight_remains>10&(buff.arcane_tempo.remains>gcd.max*2.5|buff.arcane_tempo.down)
      if S.ShiftingPower:IsCastable(true) and ((((((S.ArcaneOrb:Charges() == num(S.ChargedOrb:IsAvailable())) and S.ArcaneOrb:CooldownDown()) or S.TouchoftheMagi:CooldownRemains() < 23) and Player:BuffDown(S.ArcaneSurgeBuff) and Player:BuffDown(S.SiphonStormBuff) and Target:DebuffDown(S.TouchoftheMagiDebuff) and (Player:BuffDown(S.IntuitionBuff) or (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) > S.ShiftingPower:CastTime())) and S.TouchoftheMagi:CooldownRemains() > (12 + 6 * Player:GCD())) or (Player:PrevGCDP(1, S.ArcaneBarrage) and S.ShiftingShards:IsAvailable() and (Player:BuffDown(S.IntuitionBuff) or (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) > S.ShiftingPower:CastTime())) and (Player:BuffUp(S.ArcaneSurgeBuff) or Target:DebuffUp(S.TouchoftheMagiDebuff) or S.Evocation:CooldownRemains() < 20))) and FightRemains > 10 and (Player:BuffRemains(S.ArcaneTempoBuff) > Player:GCD() * 2.5 or Player:BuffDown(S.ArcaneTempoBuff))) then
        ShouldUseShiftingPower = true
      end
      -- cancel_buff,name=presence_of_mind,use_off_gcd=1,if=prev_gcd.1.arcane_blast&buff.presence_of_mind.stack=1
      -- TODO: Handle cancel_buff.
      -- presence_of_mind,if=debuff.touch_of_the_magi.remains<=gcd.max&buff.nether_precision.up&active_enemies<variable.aoe_target_count&!talent.unerring_proficiency
      if S.PresenceofMind:IsReady() and (Target:DebuffRemains(S.TouchoftheMagiDebuff) <= Player:GCD() and Player:BuffUp(S.NetherPrecisionBuff) and EnemiesCount8ySplash < VarAoETargetCount and not S.UnerringProficiency:IsAvailable()) then
        if Cast(S.PresenceofMind) then return "presence_of_mind spellslinger 6"; end
      end
      -- wait,sec=0.05,if=time-action.presence_of_mind.last_used<0.015,line_cd=15
      -- supernova,if=debuff.touch_of_the_magi.remains<=gcd.max&buff.unerring_proficiency.stack=30
      if S.Supernova:IsReady() and (Target:DebuffRemains(S.TouchoftheMagiDebuff) <= Player:GCD() and Player:BuffStack(S.UnerringProficiencyBuff) == 30) then
        if Cast(S.Supernova) then return "supernova spellslinger 8"; end
      end
      -- arcane_barrage,if=(buff.arcane_tempo.up&buff.arcane_tempo.remains<(gcd.max+(gcd.max*2*(buff.nether_precision.stack=1))))|(buff.intuition.react&buff.intuition.remains<gcd.max)
      if S.ArcaneBarrage:IsReady() and ((Player:BuffUp(S.ArcaneTempoBuff) and Player:BuffRemains(S.ArcaneTempoBuff) < (Player:GCD() + (Player:GCD() * 2 * num(Player:BuffStack(S.NetherPrecisionBuff) == 1)))) or (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) < Player:GCD())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 10"; end
      end
      -- arcane_barrage,if=buff.arcane_harmony.stack>=(18-(6*talent.high_voltage))&(buff.nether_precision.down|buff.nether_precision.stack=1|(active_enemies>3&buff.clearcasting.react&talent.high_voltage))
      if S.ArcaneBarrage:IsReady() and (Player:BuffStack(S.ArcaneHarmonyBuff) >= (18 - (6 * num(S.HighVoltage:IsAvailable()))) and (Player:BuffDown(S.NetherPrecisionBuff) or Player:BuffStack(S.NetherPrecisionBuff) == 1 or (EnemiesCount8ySplash > 3 and Player:BuffUp(S.ClearcastingBuff) and S.HighVoltage:IsAvailable()))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 12"; end
      end
      -- arcane_missiles,if=buff.aether_attunement.react&cooldown.touch_of_the_magi.remains<gcd.max*3&buff.clearcasting.react&set_bonus.thewarwithin_season_2_4pc
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.AetherAttunementBuff) and S.TouchoftheMagi:CooldownRemains() < Player:GCD() * 3 and Player:BuffUp(S.ClearcastingBuff) and Player:HasTier("TWW2", 4)) then
        LastSSAM = 0
        if Cast(S.ArcaneMissiles) then return "arcane_missiles spellslinger 14"; end
      end
      -- arcane_barrage,if=(cooldown.touch_of_the_magi.ready|cooldown.touch_of_the_magi.remains<((travel_time+50)>?gcd.max))
      -- Note: Replaced ((travel_time+50)>?gcd.max) with just gcd.max, since gcd.max is always going to be less than travel_time+50.
      -- Note: Also removed cooldown.touch_of_the_magi.ready, since that would be equivalent to remains=0, which is less than gcd.max.
      if S.ArcaneBarrage:IsReady() and Player:ArcaneCharges() == 4 and (S.TouchoftheMagi:CooldownRemains() < Player:GCD()) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 16"; end
      end
      -- arcane_barrage,if=talent.high_voltage&talent.orb_barrage&buff.arcane_charge.stack>1&buff.clearcasting.react&buff.aether_attunement.react&(buff.nether_precision.stack=1|(buff.nether_precision.up&active_enemies>1)|((buff.nether_precision.up|(buff.clearcasting.react<3&buff.intuition.react=0))&active_enemies>3))
      if S.ArcaneBarrage:IsReady() and (S.HighVoltage:IsAvailable() and S.OrbBarrage:IsAvailable() and Player:ArcaneCharges() > 1 and Player:BuffUp(S.ClearcastingBuff) and Player:BuffUp(S.AetherAttunementBuff) and (Player:BuffStack(S.NetherPrecisionBuff) == 1 or (Player:BuffUp(S.NetherPrecisionBuff) and EnemiesCount8ySplash > 1) or ((Player:BuffUp(S.NetherPrecisionBuff) or (Player:BuffStack(S.ClearcastingBuff) < 3 and Player:BuffDown(S.IntuitionBuff))) and EnemiesCount8ySplash > 3))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 18"; end
      end
      -- arcane_missiles,if=(buff.clearcasting.react&buff.nether_precision.down&((cooldown.touch_of_the_magi.remains>gcd.max*7&cooldown.arcane_surge.remains>gcd.max*7)|buff.clearcasting.react>1|!talent.magis_spark|(cooldown.touch_of_the_magi.remains<gcd.max*4&buff.aether_attunement.react=0)|set_bonus.thewarwithin_season_2_4pc))|(fight_remains<5&buff.clearcasting.react),interrupt_if=tick_time>gcd.remains&(buff.aether_attunement.react=0|(active_enemies>3&(!talent.time_loop|talent.resonance))),interrupt_immediate=1,interrupt_global=1,chain=1
      if S.ArcaneMissiles:IsReady() and ((Player:BuffUp(S.ClearcastingBuff) and Player:BuffDown(S.NetherPrecisionBuff) and ((S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 7 and S.ArcaneSurge:CooldownRemains() > Player:GCD() * 7) or Player:BuffStack(S.ClearcastingBuff) > 1 or not S.MagisSpark:IsAvailable() or (S.TouchoftheMagi:CooldownRemains() < Player:GCD() * 4 and Player:BuffDown(S.AetherAttunementBuff)) or Player:HasTier("TWW2", 4))) or (FightRemains < 5 and Player:BuffUp(S.ClearcastingBuff))) then
        LastSSAM = 1
        if Cast(S.ArcaneMissiles) then return "arcane_missiles spellslinger 20"; end
      end
      -- arcane_blast,if=((debuff.magis_spark_arcane_blast.up&((debuff.magis_spark_arcane_blast.remains<(cast_time+gcd.max))|active_enemies=1|talent.leydrinker))|buff.leydrinker.up)&buff.arcane_charge.stack=4&!talent.charged_orb&active_enemies<3,line_cd=2
      if S.ArcaneBlast:IsReady() and S.ArcaneBlast:TimeSinceLastCast() >= 2 and (((Target:DebuffUp(S.MagisSparkABDebuff) and ((Target:DebuffRemains(S.MagisSparkABDebuff) < (S.ArcaneBlast:CastTime() + Player:GCD())) or EnemiesCount8ySplash == 1 or S.Leydrinker:IsAvailable())) or Player:BuffUp(S.LeydrinkerBuff)) and Player:ArcaneCharges() == 4 and not S.ChargedOrb:IsAvailable() and EnemiesCount8ySplash < 3) then
        if Cast(S.ArcaneBlast) then return "arcane_blast spellslinger 22"; end
      end
      -- arcane_barrage,if=talent.orb_barrage&active_enemies>1&(debuff.magis_spark_arcane_blast.down|!talent.magis_spark)&buff.arcane_charge.stack=4&((talent.high_voltage&active_enemies>2)|((cooldown.touch_of_the_magi.remains>gcd.max*6|!talent.magis_spark)|(talent.charged_orb&cooldown.arcane_orb.charges_fractional>1.8)))
      if S.ArcaneBarrage:IsReady() and (S.OrbBarrage:IsAvailable() and EnemiesCount8ySplash > 1 and (Target:DebuffDown(S.MagisSparkABDebuff) or not S.MagisSpark:IsAvailable()) and Player:ArcaneCharges() == 4 and ((S.HighVoltage:IsAvailable() and EnemiesCount8ySplash > 2) or ((S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 6 or not S.MagisSpark:IsAvailable()) or (S.ChargedOrb:IsAvailable() and S.ArcaneOrb:ChargesFractional() > 1.8)))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 24"; end
      end
      -- arcane_barrage,if=active_enemies>1&(debuff.magis_spark_arcane_blast.down|!talent.magis_spark)&buff.arcane_charge.stack=4&(cooldown.arcane_orb.remains<gcd.max|(target.health.pct<35&talent.arcane_bombardment))&(buff.nether_precision.stack=1|(buff.nether_precision.down&talent.high_voltage)|(buff.nether_precision.stack=2&target.health.pct<35&talent.arcane_bombardment&talent.high_voltage))&(cooldown.touch_of_the_magi.remains>gcd.max*6|(talent.charged_orb&cooldown.arcane_orb.charges_fractional>1.8))
      if S.ArcaneBarrage:IsReady() and (EnemiesCount8ySplash > 1 and (Target:DebuffDown(S.MagisSparkABDebuff) or not S.MagisSpark:IsAvailable()) and Player:ArcaneCharges() == 4 and (S.ArcaneOrb:CooldownRemains() < Player:GCD() or (Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable())) and (Player:BuffStack(S.NetherPrecisionBuff) == 1 or (Player:BuffDown(S.NetherPrecisionBuff) and S.HighVoltage:IsAvailable()) or (Player:BuffStack(S.NetherPrecisionBuff) == 2 and Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable() and S.HighVoltage:IsAvailable())) and (S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 6 or (S.ChargedOrb:IsAvailable() and S.ArcaneOrb:ChargesFractional() > 1.8))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 26"; end
      end
      -- arcane_missiles,if=talent.high_voltage&(buff.clearcasting.react>1|(buff.clearcasting.react&buff.aether_attunement.react))&buff.arcane_charge.stack<3,interrupt_if=tick_time>gcd.remains&(buff.aether_attunement.react=0|(active_enemies>3&(!talent.time_loop|talent.resonance))),interrupt_immediate=1,interrupt_global=1,chain=1
      if S.ArcaneMissiles:IsReady() and (S.HighVoltage:IsAvailable() and (Player:BuffStack(S.ClearcastingBuff) > 1 or (Player:BuffUp(S.ClearcastingBuff) and Player:BuffUp(S.AetherAttunementBuff))) and Player:ArcaneCharges() < 3) then
        LastSSAM = 2
        if Cast(S.ArcaneMissiles) then return "arcane_missiles spellslinger 28"; end
      end
      -- arcane_orb,if=(active_enemies=1&buff.arcane_charge.stack<3)|(buff.arcane_charge.stack<1|(buff.arcane_charge.stack<2&talent.high_voltage))
      if S.ArcaneOrb:IsReady() and ((EnemiesCount8ySplash == 1 and Player:ArcaneCharges() < 3) or (Player:ArcaneCharges() < 1 or (Player:ArcaneCharges() < 2 and S.HighVoltage:IsAvailable()))) then
        if Cast(S.ArcaneOrb) then return "arcane_orb spellslinger 30"; end
      end
      -- arcane_barrage,if=buff.intuition.react
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.IntuitionBuff) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 32"; end
      end
      -- arcane_barrage,if=active_enemies=1&talent.high_voltage&buff.arcane_charge.stack=4&buff.clearcasting.react&buff.nether_precision.stack=1&(buff.aether_attunement.react|(target.health.pct<35&talent.arcane_bombardment))
      if S.ArcaneBarrage:IsReady() and (EnemiesCount8ySplash == 1 and S.HighVoltage:IsAvailable() and Player:ArcaneCharges() == 4 and Player:BuffUp(S.ClearcastingBuff) and Player:BuffStack(S.NetherPrecisionBuff) == 1 and (Player:BuffUp(S.AetherAttunementBuff) or (Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable()))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 34"; end
      end
      -- arcane_barrage,if=cooldown.arcane_orb.remains<gcd.max&buff.arcane_charge.stack=4&buff.nether_precision.down&talent.orb_barrage&(cooldown.touch_of_the_magi.remains>gcd.max*6|!talent.magis_spark)
      if S.ArcaneBarrage:IsReady() and (S.ArcaneOrb:CooldownRemains() < Player:GCD() and Player:ArcaneCharges() == 4 and Player:BuffDown(S.NetherPrecisionBuff) and S.OrbBarrage:IsAvailable() and (S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 6 or not S.MagisSpark:IsAvailable())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 36"; end
      end
      -- arcane_barrage,if=active_enemies=1&(talent.orb_barrage|(target.health.pct<35&talent.arcane_bombardment))&(cooldown.arcane_orb.remains<gcd.max)&buff.arcane_charge.stack=4&(cooldown.touch_of_the_magi.remains>gcd.max*6|!talent.magis_spark)&(buff.nether_precision.down|(buff.nether_precision.stack=1&buff.clearcasting.stack=0))
      if S.ArcaneBarrage:IsReady() and (EnemiesCount8ySplash == 1 and (S.OrbBarrage:IsAvailable() or (Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable())) and (S.ArcaneOrb:CooldownRemains() < Player:GCD()) and Player:ArcaneCharges() == 4 and (S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 6 or not S.MagisSpark:IsAvailable()) and (Player:BuffDown(S.NetherPrecisionBuff) or (Player:BuffStack(S.NetherPrecisionBuff) == 1 and Player:BuffDown(S.ClearcastingBuff)))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 38"; end
      end
      -- arcane_explosion,if=active_enemies>1&((buff.arcane_charge.stack<1&!talent.high_voltage)|(buff.arcane_charge.stack<3&(buff.clearcasting.react=0|talent.reverberate)))
      if S.ArcaneExplosion:IsReady() and (EnemiesCount8ySplash > 1 and ((Player:ArcaneCharges() < 1 and not S.HighVoltage:IsAvailable()) or (Player:ArcaneCharges() < 3 and (Player:BuffDown(S.ClearcastingBuff) or S.Reverberate:IsAvailable())))) then
        if Cast(S.ArcaneExplosion) then return "arcane_explosion spellslinger 40"; end
      end
      -- arcane_explosion,if=active_enemies=1&buff.arcane_charge.stack<2&buff.clearcasting.react=0&mana.pct>10
      if S.ArcaneExplosion:IsReady() and (EnemiesCount8ySplash == 1 and Player:ArcaneCharges() < 2 and Player:BuffDown(S.ClearcastingBuff) and Player:ManaPercentage() > 10) then
        if Cast(S.ArcaneExplosion) then return "arcane_explosion spellslinger 42"; end
      end
      -- arcane_barrage,if=((target.health.pct<35&(debuff.touch_of_the_magi.remains<(gcd.max*1.25))&(debuff.touch_of_the_magi.remains>action.arcane_barrage.travel_time))|((buff.arcane_surge.remains<gcd.max)&buff.arcane_surge.up))&buff.arcane_charge.stack=4
      if S.ArcaneBarrage:IsReady() and (((Target:HealthPercentage() < 35 and (Target:DebuffRemains(S.TouchoftheMagiDebuff) < (Player:GCD() * 1.25)) and (Target:DebuffRemains(S.TouchoftheMagiDebuff) > S.ArcaneBarrage:TravelTime())) or ((Player:BuffRemains(S.ArcaneSurgeBuff) < Player:GCD()) and Player:BuffUp(S.ArcaneSurgeBuff))) and Player:ArcaneCharges() == 4) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 44"; end
      end
      -- arcane_blast
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "arcane_blast spellslinger 46"; end
      end
      -- arcane_barrage
      if S.ArcaneBarrage:IsReady() then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger 48"; end
      end
    end        

    local function customSpellslinger()
      --------------------------------------------------
      -- Touch of the Magi (TotM) opener with Barrage
      --------------------------------------------------
      if S.TouchoftheMagi:IsReady() then
        
        -- Optimal opener: Cast Barrage first (to travel) then TotM at 4 Arcane Charges
        if Player:ArcaneCharges() == 4 then
          if S.ArcaneBarrage:IsReady() and not Player:PrevGCDP(1, S.ArcaneBarrage) then
            if Cast(S.ArcaneBarrage) then return "Opener: Barrage before TotM" end
          end
          if S.TouchoftheMagi:IsReady() and Player:PrevGCDP(1, S.ArcaneBarrage) then
            if Cast(S.TouchoftheMagi) then return "Opener: TotM after Barrage" end
          end
        
        -- Fallback: Use TotM on cooldown even without full charges
        else
          if Cast(S.TouchoftheMagi) then return "TotM: Using on cooldown (fallback)" end
        end
      end
      
      --------------------------------------------------------------------------
      -- Supernova with max Unerring Proficiency stacks as TotM expires
      --------------------------------------------------------------------------
      if S.Supernova:IsReady()
          and Player:BuffStack(S.UnerringProficiencyBuff) == 30
          and Target:DebuffUp(S.TouchoftheMagiDebuff)
          and Target:DebuffRemains(S.TouchoftheMagiDebuff) <= Player:GCD()*2 then
        if Cast(S.Supernova) then return "Supernova: 30 Unerring stacks before TotM expires" end
      end
      
      --------------------------------------------------------------------------
      -- Presence of Mind for mobility with Unerring/Supernova build
      --------------------------------------------------------------------------
      if S.PresenceofMind:IsReady()
          and S.UnerringProficiency:IsAvailable()
          and S.Supernova:IsAvailable()
          and Player:IsMoving()
          and Player:BuffDown(S.PresenceofMindBuff) then
        if Cast(S.PresenceofMind) then return "PoM: Using for mobility with Unerring build" end
      end
      
      -----------------------------------------------------------------------------
      -- Arcane Barrage to avoid losing Intuition/Tempo buffs
      -----------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) <= Player:GCD()*1.5) or
            (Player:BuffUp(S.ArcaneTempoBuff) and (
              Player:BuffRemains(S.ArcaneTempoBuff) <= Player:GCD()*1.5 or 
              (Player:BuffStack(S.NetherPrecisionBuff) == 1 and Player:BuffRemains(S.ArcaneTempoBuff) <= Player:GCD()*3)
            ))
          ) then
        if Cast(S.ArcaneBarrage) then return "Barrage: Preserving expiring buff (Intuition/Tempo)" end
      end
      
      ------------------------------
      -- Shifting Power during downtime
      ------------------------------
      if S.ShiftingPower:IsCastable(true) and (
          Player:BuffDown(S.ArcaneSurgeBuff) and
          Player:BuffDown(S.SiphonStormBuff) and 
          Target:DebuffDown(S.TouchoftheMagiDebuff) and S.TouchoftheMagi:CooldownRemains(nil, true) > 30
        ) then
          ShouldUseShiftingPower = true
      end
      
      ----------------------------------------------------------------------------------
      -- Barrage at 4 charges with high Arcane Harmony and low Precision
      ----------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and Player:ArcaneCharges() == 4
          and Player:BuffStack(S.NetherPrecisionBuff) <= 1
          and ((S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 12)
            or (not S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 18)) then
        if Cast(S.ArcaneBarrage) then return "Barrage: High Harmony stacks (12+/18+)" end
      end
      
      --------------------------------------------------------------------------
      -- Arcane Missiles with Aether Attunement when TotM is coming off CD
      --------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady() and Player:BuffUp(S.AetherAttunementBuff)
          and S.TouchoftheMagi:CooldownRemains() < Player:GCD()*2 then
        if Cast(S.ArcaneMissiles) then return "Missiles: Aether Attunement with TotM soon" end
      end
      
      -------------------------------------------
      -- Arcane Blast with Leydrinker buff
      -------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.LeydrinkerBuff) then
        if Cast(S.ArcaneBlast) then return "Blast: Consuming Leydrinker buff" end
      end
    
      -------------------------------
      -- Arcane Orb below 3 charges (without High Voltage)
      -------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 and not S.HighVoltage:IsAvailable() then
        if Cast(S.ArcaneOrb) then return "Orb: Building charges (no High Voltage)" end
      end
    
      ---------------------------------------------------------------------------------
      -- Arcane Missiles with Clearcasting without Nether Precision (no High Voltage)
      ---------------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
          and not S.HighVoltage:IsAvailable()
          and Player:BuffUp(S.ClearcastingBuff)
          and Player:BuffDown(S.NetherPrecisionBuff) then
        if Cast(S.ArcaneMissiles) then return "Missiles: Clearcasting without NP (no HV)" end
      end
      
      ---------------------------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (High Voltage build)
      ---------------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
          and S.HighVoltage:IsAvailable()
          and Player:BuffUp(S.ClearcastingBuff)
          and (Player:BuffDown(S.NetherPrecisionBuff) or Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneMissiles) then return "Missiles: Clearcasting with HV build" end
      end
      
      ------------------------------------------------------------------------------------------------
      -- Barrage (High Voltage) - 4 charges, 1 NP stack, with Clearcasting and AA or execute phase
      ------------------------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and S.HighVoltage:IsAvailable()
          and Player:ArcaneCharges() == 4
          and Player:BuffStack(S.NetherPrecisionBuff) == 1
          and Player:BuffUp(S.ClearcastingBuff)
          and (Player:BuffUp(S.AetherAttunementBuff) or Target:HealthPercentage() < 35) then
        if Cast(S.ArcaneBarrage) then return "Barrage: 4 charges with CC/1 NP/AA or execute" end
      end
      
      -----------------------------------------------------------------------------------------------
      -- Barrage with Arcane Orb ready (4 charges, no NP or no CC with 1 NP)
      -----------------------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and Player:ArcaneCharges() == 4
          and S.ArcaneOrb:IsReady()
          and (
            Player:BuffStack(S.NetherPrecisionBuff) == 0
            or (Player:BuffDown(S.ClearcastingBuff) and Player:BuffStack(S.NetherPrecisionBuff) == 1)
          ) then
        if Cast(S.ArcaneBarrage) then return "Barrage: 4 charges with Orb ready (NP/CC optimization)" end
      end
      
      -------------------------------
      -- Arcane Orb below 3 charges
      -------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneOrb) then return "Orb: Building charges" end
      end
      
      -----------------------------
      -- Barrage with Intuition
      -----------------------------
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.IntuitionBuff) then
        if Cast(S.ArcaneBarrage) then return "Barrage: Intuition proc active" end
      end
      
      -----------------------------------------------------------------------------------------------------------------
      -- Barrage during execute as TotM is about to expire
      -----------------------------------------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and Player:ArcaneCharges() == 4
          and Target:DebuffUp(S.TouchoftheMagiDebuff)
          and Target:HealthPercentage() < 35
          and Target:DebuffRemains(S.TouchoftheMagiDebuff) < S.ArcaneBarrage:TravelTime() + Player:GCD()*2 then
        if Cast(S.ArcaneBarrage) then return "Barrage: Execute phase with TotM ending soon" end
      end
      
      -----------------------------------------------------------------------------------------
      -- Barrage at end of Arcane Surge/Time Anomaly
      -----------------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() 
          and Player:ArcaneCharges() == 4
          and Player:BuffUp(S.ArcaneSurgeBuff) 
          and Player:BuffRemains(S.ArcaneSurgeBuff) < S.ArcaneBarrage:TravelTime() + Player:GCD()*2 then
        if Cast(S.ArcaneBarrage) then return "Barrage: End of Arcane Surge window" end
      end
      
      ----------------------------------------------
      -- Arcane Explosion for charge building
      ----------------------------------------------
      if S.ArcaneExplosion:IsReady() and Player:ArcaneCharges() < 2 then
        if Cast(S.ArcaneExplosion) then return "Explosion: Building charges (<2)" end
      end
      
      ------------------------------
      -- Arcane Blast (main filler)
      ------------------------------
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "Blast: Regular casting" end
      end
      
      ---------------------------------------------
      -- Emergency charge dump on low mana
      ---------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:ManaPercentage() < 10 then
        if Cast(S.ArcaneBarrage) then return "Barrage: Emergency mana conservation (<10%)" end
      end
    end
    
    local function SpellslingerAoE()
      -- supernova,if=buff.unerring_proficiency.stack=30
      if S.Supernova:IsReady() and (Player:BuffStack(S.UnerringProficiencyBuff) == 30) then
        if Cast(S.Supernova) then return "supernova spellslinger_aoe 2"; end
      end
      -- shifting_power,if=((buff.arcane_surge.down&buff.siphon_storm.down&debuff.touch_of_the_magi.down&cooldown.evocation.remains>15&cooldown.touch_of_the_magi.remains>10)&(cooldown.arcane_orb.remains&action.arcane_orb.charges=0)&fight_remains>10)|(prev_gcd.1.arcane_barrage&(buff.arcane_surge.up|debuff.touch_of_the_magi.up|cooldown.evocation.remains<20)&talent.shifting_shards)
      if S.ShiftingPower:IsCastable(true) and (((Player:BuffDown(S.ArcaneSurgeBuff) and Player:BuffDown(S.SiphonStormBuff) and Target:DebuffDown(S.TouchoftheMagiDebuff) and S.Evocation:CooldownRemains() > 15 and S.TouchoftheMagi:CooldownRemains() > 10) and (S.ArcaneOrb:CooldownDown() and S.ArcaneOrb:Charges() == 0) and FightRemains > 10) or (Player:PrevGCDP(1, S.ArcaneBarrage) and (Player:BuffUp(S.ArcaneSurgeBuff) or Target:DebuffUp(S.TouchoftheMagiDebuff) or S.Evocation:CooldownRemains() < 20) and S.ShiftingShards:IsAvailable())) then
        ShouldUseShiftingPower = true
      end
      -- arcane_orb,if=buff.arcane_charge.stack<3
      if S.ArcaneOrb:IsReady() and (Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneOrb) then return "arcane_orb spellslinger_aoe 6"; end
      end
      -- arcane_blast,if=((debuff.magis_spark_arcane_blast.up|buff.leydrinker.up)&!prev_gcd.1.arcane_blast)
      if S.ArcaneBlast:IsReady() and ((Target:DebuffUp(S.MagisSparkABDebuff) or Player:BuffUp(S.LeydrinkerBuff)) and not Player:PrevGCDP(1, S.ArcaneBlast)) then
        if Cast(S.ArcaneBlast) then return "arcane_blast spellslinger_aoe 8"; end
      end
      -- trigger magis spark
      if S.ArcaneBlast:IsReady() and Target:DebuffUp(S.TouchoftheMagiDebuff) and S.ArcaneBlast:TimeSinceLastCast() >= 10 then
        if Cast(S.ArcaneBlast) then return "arcane_blast in totm spellslinger_aoe 8"; end
      end
      -- spend intuition/keep up arcane tempo high prio
      if S.ArcaneBarrage:IsReady() and Player:ArcaneCharges() == 4 and (Player:BuffUp(S.IntuitionBuff)) then
        if Cast(S.ArcaneBarrage) then return "BARRAGE ON TEMPO/INTasdasdUITION"; end
      end
      -- arcane_barrage,if=buff.aether_attunement.react&talent.high_voltage&buff.clearcasting.react&buff.arcane_charge.stack>1
      if S.ArcaneBarrage:IsReady() and (Player:BuffUp(S.AetherAttunementBuff) and S.HighVoltage:IsAvailable() and Player:BuffUp(S.ClearcastingBuff) and Player:ArcaneCharges() > 1) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger_aoe 10"; end
      end
      -- arcane_missiles,if=buff.clearcasting.react&((talent.high_voltage&buff.arcane_charge.stack<4)|buff.nether_precision.down),interrupt_if=tick_time>gcd.remains&buff.aether_attunement.down,interrupt_immediate=1,interrupt_global=1,chain=1
      if Player:IsChanneling(S.ArcaneMissiles) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and Player:BuffDown(S.AetherAttunementBuff)) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt spellslinger_aoe 12"; end
      end
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.ClearcastingBuff) and ((S.HighVoltage:IsAvailable() and Player:ArcaneCharges() < 4) or Player:BuffDown(S.NetherPrecisionBuff))) then
        if Cast(S.ArcaneMissiles) then return "arcane_missiles spellslinger_aoe 14"; end
      end
      -- presence_of_mind,if=buff.arcane_charge.stack=3|buff.arcane_charge.stack=2
      if S.PresenceofMind:IsReady() and (Player:ArcaneCharges() == 3 or Player:ArcaneCharges() == 2) then
        if Cast(S.PresenceofMind) then return "presence_of_mind spellslinger_aoe 16"; end
      end
      -- arcane_barrage,if=buff.arcane_charge.stack=4
      if S.ArcaneBarrage:IsReady() and (Player:ArcaneCharges() == 4) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger_aoe 18"; end
      end
      -- arcane_explosion,if=(talent.reverberate|buff.arcane_charge.stack<2)
      if S.ArcaneExplosion:IsReady() and (S.Reverberate:IsAvailable() or Player:ArcaneCharges() < 2) then
        if Cast(S.ArcaneExplosion) then return "arcane_explosion spellslinger_aoe 20"; end
      end
      -- -- arcane_blast
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "arcane_blast spellslinger_aoe 22"; end
      end
      -- arcane_barrage
      if S.ArcaneBarrage:IsReady() then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage spellslinger_aoe 24"; end
      end
    end

    local function customSpellslingerAoE()
      --------------------------------------------------
      -- Touch of the Magi (TotM) opener with Barrage
      --------------------------------------------------
      if S.TouchoftheMagi:IsReady() then
        
        -- Optimal opener: Cast Barrage first (to travel) then TotM at 4 Arcane Charges
        if Player:ArcaneCharges() == 4 then
          if S.ArcaneBarrage:IsReady() and not Player:PrevGCDP(1, S.ArcaneBarrage) then
            if Cast(S.ArcaneBarrage) then return "AoE Opener: Barrage before TotM" end
          end
          if S.TouchoftheMagi:IsReady() and Player:PrevGCDP(1, S.ArcaneBarrage) then
            if Cast(S.TouchoftheMagi) then return "AoE Opener: TotM after Barrage" end
          end
        
        -- Fallback: Use TotM on cooldown even without full charges
        else
          if Cast(S.TouchoftheMagi) then return "AoE TotM: Using on cooldown" end
        end
      end
      
      --------------------------------------------------
      -- Supernova at max Unerring Proficiency stacks
      --------------------------------------------------
      if S.Supernova:IsReady() and Player:BuffStack(S.UnerringProficiencyBuff) == 30 then
        if Cast(S.Supernova) then return "AoE Supernova: Maximum Unerring stacks (30)" end
      end
      
      --------------------------------------------------
      -- Arcane Blast during Leydrinker buff
      --------------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.LeydrinkerBuff) then
        if Cast(S.ArcaneBlast) then return "AoE Blast: Consuming Leydrinker buff" end
      end
      
      --------------------------------------------------
      -- Cast Blast during TotM for Magi's Spark
      --------------------------------------------------
      if S.ArcaneBlast:IsReady()
          and Target:DebuffUp(S.TouchoftheMagiDebuff)
          and S.ArcaneBlast:TimeSinceLastCast() >= 10 then
        if Cast(S.ArcaneBlast) then return "AoE Blast: Triggering Magi's Spark during TotM" end
      end
      
      ------------------------------------------------------------------
      -- Arcane Barrage before buffs expire
      ------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) <= Player:GCD()*1.5) or
            (Player:BuffUp(S.ArcaneTempoBuff) and Player:BuffRemains(S.ArcaneTempoBuff) <= Player:GCD()*1.5)) then
        if Cast(S.ArcaneBarrage) then return "AoE Barrage: Preserving expiring buff (Intuition/Tempo)" end
      end
      
      ------------------------------
      -- Shifting Power during cooldown downtime
      ------------------------------
      if S.ShiftingPower:IsCastable(true) and (
          Player:BuffDown(S.ArcaneSurgeBuff) and 
          Player:BuffDown(S.SiphonStormBuff) and 
          Target:DebuffDown(S.TouchoftheMagiDebuff)
        ) then
          ShouldUseShiftingPower = true
      end
      
      -------------------------------------------------------------------
      -- Barrage with high Arcane Harmony stacks
      -------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and Player:ArcaneCharges() == 4
          and Player:BuffStack(S.NetherPrecisionBuff) <= 1
          and ((S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 12)
            or (not S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 18)) then
        if Cast(S.ArcaneBarrage) then return "AoE Barrage: High Harmony stacks (12+/18+)" end
      end
      
      ---------------------------------------------------------------------------
      -- Arcane Missiles with Aether Attunement before TotM
      ---------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "AoE Clipping: Stopping Missiles channel" 
        end
      end
      
      if S.ArcaneMissiles:IsReady()
          and Player:BuffUp(S.AetherAttunementBuff)
          and S.TouchoftheMagi:CooldownRemains() < Player:GCD()*2 then
        if Cast(S.ArcaneMissiles) then return "AoE Missiles: Aether Attunement with TotM soon" end
      end
    
      -------------------------------
      -- Arcane Orb for charge building (no High Voltage)
      -------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 and not S.HighVoltage:IsAvailable() then
        if Cast(S.ArcaneOrb) then return "AoE Orb: Building charges (<3) without High Voltage" end
      end
    
      ---------------------------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (no High Voltage)
      ---------------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "AoE Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
          and not S.HighVoltage:IsAvailable()
          and Player:BuffUp(S.ClearcastingBuff)
          and Player:BuffDown(S.NetherPrecisionBuff) then
        if Cast(S.ArcaneMissiles) then return "AoE Missiles: Clearcasting without Nether Precision" end
      end
      
      ----------------------------------------------------------------------------------
      -- Barrage with Aether Attunement + Clearcasting (avoid capping CC)
      ----------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and S.HighVoltage:IsAvailable()
          and Player:BuffUp(S.AetherAttunementBuff)
          and Player:BuffUp(S.ClearcastingBuff)
          and Player:ArcaneCharges() > 1
          and Player:BuffStack(S.ClearcastingBuff) < 3 then
        if Cast(S.ArcaneBarrage) then return "AoE Barrage: AA + CC without capping stacks" end
      end
      
      ------------------------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (High Voltage build)
      ------------------------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "AoE Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
          and S.HighVoltage:IsAvailable()
          and Player:BuffUp(S.ClearcastingBuff)
          and (Player:BuffDown(S.NetherPrecisionBuff) or Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneMissiles) then return "AoE Missiles: Clearcasting with HV (low NP or charges)" end
      end
      
      ---------------------------------------------
      -- Arcane Orb for charge building
      ---------------------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneOrb) then return "AoE Orb: Building charges (<3)" end
      end
      
      --------------------------------------------------------------------------------
      -- Barrage at max charges in strategic situations
      --------------------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:ArcaneCharges() == 4 then
        if Player:BuffUp(S.ClearcastingBuff)
            or Target:HealthPercentage() < 35
            or S.ArcaneOrb:CooldownDown() then
          if Cast(S.ArcaneBarrage) then return "AoE Barrage: Max charges with CC/Execute/Orb ready" end
        end
      end
      
      -----------------------------
      -- Barrage with Intuition
      -----------------------------
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.IntuitionBuff) then
        if Cast(S.ArcaneBarrage) then return "AoE Barrage: Intuition proc active" end
      end
      
      -------------------------------------------------
      -- Presence of Mind for faster charge building
      -------------------------------------------------
      if S.PresenceofMind:IsReady() and Player:ArcaneCharges() < 4 then
        if Cast(S.PresenceofMind) then return "AoE PoM: Accelerating charge generation" end
      end
      
      -------------------------------------------------
      -- Arcane Blast during Presence of Mind
      -------------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.PresenceofMindBuff) then
        if Cast(S.ArcaneBlast) then return "AoE Blast: Using instant PoM procs" end
      end
      
      ----------------------------------------------
      -- Arcane Explosion for charge building
      ----------------------------------------------
      if S.ArcaneExplosion:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneExplosion) then return "AoE Explosion: Building charges (<3)" end
      end
      
      ----------------------------
      -- Arcane Blast (filler)
      ----------------------------
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "AoE Blast: Primary filler spell" end
      end
      
      ---------------------------------------------
      -- Emergency mana conservation
      ---------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:ManaPercentage() < 10 then
        if Cast(S.ArcaneBarrage) then return "AoE Barrage: Emergency mana conservation (<10%)" end
      end
    end
    
    local function Sunfury()
      -- Note: Handle arcane_missiles interrupts.
      if Player:IsChanneling(S.ArcaneMissiles) and LastSFAM == 1 and (S.ArcaneMissiles:TickTime() > Player:GCDRemains()) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt sunfury 2"; end
      end
      if Player:IsChanneling(S.ArcaneMissiles) and (LastSFAM == 2 or LastSFAM == 3) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and (Player:BuffDown(S.AetherAttunementBuff) or (EnemiesCount8ySplash > 3 and (not S.TimeLoop:IsAvailable() or S.Resonance:IsAvailable())))) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt sunfury 4"; end
      end
      -- shifting_power,if=((buff.arcane_surge.down&buff.siphon_storm.down&debuff.touch_of_the_magi.down&cooldown.evocation.remains>15&cooldown.touch_of_the_magi.remains>10)&fight_remains>10)&buff.arcane_soul.down&(buff.intuition.react=0|(buff.intuition.react&buff.intuition.remains>cast_time))
      if S.ShiftingPower:IsCastable(true) and (((Player:BuffDown(S.ArcaneSurgeBuff) and Player:BuffDown(S.SiphonStormBuff) and Target:DebuffDown(S.TouchoftheMagiDebuff) and S.Evocation:CooldownRemains() > 15 and S.TouchoftheMagi:CooldownRemains() > 10) and FightRemains > 10) and Player:BuffDown(S.ArcaneSoulBuff) and (Player:BuffDown(S.IntuitionBuff) or (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) > S.ShiftingPower:CastTime()))) then
        ShouldUseShiftingPower = true
      end
      -- cancel_buff,name=presence_of_mind,use_off_gcd=1,if=(prev_gcd.1.arcane_blast&buff.presence_of_mind.stack=1)|active_enemies<4
      -- TODO: Handle cancel_buff.
      -- presence_of_mind,if=debuff.touch_of_the_magi.remains<=gcd.max&buff.nether_precision.up&active_enemies<4
      if S.PresenceofMind:IsReady() and (Target:DebuffRemains(S.TouchoftheMagiDebuff) <= Player:GCD() and Player:BuffUp(S.NetherPrecisionBuff) and EnemiesCount8ySplash < 4) then
        if Cast(S.PresenceofMind) then return "presence_of_mind sunfury 8"; end
      end
      -- wait,sec=0.05,if=time-action.presence_of_mind.last_used<0.015,line_cd=15
      -- arcane_missiles,if=buff.nether_precision.down&buff.clearcasting.react&buff.arcane_soul.up&buff.arcane_soul.remains>gcd.max*(4-buff.clearcasting.react),interrupt_if=tick_time>gcd.remains,interrupt_immediate=1,interrupt_global=1,chain=1
      if S.ArcaneMissiles:IsReady() and (Player:BuffDown(S.NetherPrecisionBuff) and Player:BuffUp(S.ClearcastingBuff) and Player:BuffUp(S.ArcaneSoulBuff) and Player:BuffRemains(S.ArcaneSoulBuff) > Player:GCD() * (4 - Player:BuffStack(S.ClearcastingBuff))) then
        LastSFAM = 1
        if Cast(S.ArcaneMissiles) then return "arcane_missiles sunfury 10"; end
      end
      -- arcane_barrage,if=buff.arcane_soul.up
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.ArcaneSoulBuff) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 12"; end
      end
      -- arcane_barrage,if=(buff.arcane_tempo.up&buff.arcane_tempo.remains<gcd.max)|(buff.intuition.react&buff.intuition.remains<gcd.max)
      if S.ArcaneBarrage:IsReady() and ((Player:BuffUp(S.ArcaneTempoBuff) and Player:BuffRemains(S.ArcaneTempoBuff) < Player:GCD()) or (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) < Player:GCD())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 14"; end
      end
      -- arcane_barrage,if=(talent.orb_barrage&active_enemies>1&buff.arcane_harmony.stack>=18&((active_enemies>3&(talent.resonance|talent.high_voltage))|buff.nether_precision.down|buff.nether_precision.stack=1|(buff.nether_precision.stack=2&buff.clearcasting.react=3)))
      if S.ArcaneBarrage:IsReady() and ((S.OrbBarrage:IsAvailable() and EnemiesCount8ySplash > 1 and Player:BuffStack(S.ArcaneHarmonyBuff) >= 18 and ((EnemiesCount8ySplash > 3 and (S.Resonance:IsAvailable() or S.HighVoltage:IsAvailable())) or Player:BuffDown(S.NetherPrecisionBuff) or Player:BuffStack(S.NetherPrecisionBuff) == 1 or (Player:BuffStack(S.NetherPrecisionBuff) == 2 and Player:BuffStack(S.ClearcastingBuff) == 3)))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 16"; end
      end
      -- arcane_missiles,if=buff.clearcasting.react&set_bonus.thewarwithin_season_2_4pc&buff.aether_attunement.react&cooldown.touch_of_the_magi.remains<gcd.max*(3-(1.5*(active_enemies>3&(!talent.time_loop|talent.resonance)))),interrupt_if=tick_time>gcd.remains&(buff.aether_attunement.react=0|(active_enemies>3&(!talent.time_loop|talent.resonance))),interrupt_immediate=1,interrupt_global=1,chain=1
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.ClearcastingBuff) and Player:HasTier("TWW2", 4) and Player:BuffUp(S.AetherAttunementBuff) and S.TouchoftheMagi:CooldownRemains() < Player:GCD() * (3 - (1.5 * num(EnemiesCount8ySplash > 3 and (not S.TimeLoop:IsAvailable() or S.Resonance:IsAvailable()))))) then
        LastSFAM = 2
        if Cast(S.ArcaneMissiles) then return "arcane_missiles sunfury 18"; end
      end
      -- arcane_blast,if=((debuff.magis_spark_arcane_blast.up&((debuff.magis_spark_arcane_blast.remains<(cast_time+gcd.max))|active_enemies=1|talent.leydrinker))|buff.leydrinker.up)&buff.arcane_charge.stack=4&(buff.nether_precision.up|buff.clearcasting.react=0),line_cd=2
      if S.ArcaneBlast:IsReady() and S.ArcaneBlast:TimeSinceLastCast() >= 2 and (((Target:DebuffUp(S.MagisSparkABDebuff) and ((Target:DebuffRemains(S.MagisSparkABDebuff) < (S.ArcaneBlast:CastTime() + Player:GCD())) or EnemiesCount8ySplash == 1 or S.Leydrinker:IsAvailable())) or Player:BuffUp(S.LeydrinkerBuff)) and Player:ArcaneCharges() == 4 and (Player:BuffUp(S.NetherPrecisionBuff) or Player:BuffDown(S.ClearcastingBuff))) then
        if Cast(S.ArcaneBlast) then return "arcane_blast sunfury 20"; end
      end
      -- arcane_barrage,if=buff.arcane_charge.stack=4&(cooldown.touch_of_the_magi.ready|cooldown.touch_of_the_magi.remains<((travel_time+50)>?gcd.max))
      if S.ArcaneBarrage:IsReady() and (Player:ArcaneCharges() == 4 and (S.TouchoftheMagi:CooldownRemains() < Player:GCD())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 22"; end
      end
      -- arcane_barrage,if=(talent.high_voltage&active_enemies>1&buff.arcane_charge.stack=4&buff.clearcasting.react&buff.nether_precision.stack=1)
      if S.ArcaneBarrage:IsReady() and (S.HighVoltage:IsAvailable() and EnemiesCount8ySplash > 1 and Player:ArcaneCharges() == 4 and Player:BuffUp(S.ClearcastingBuff) and Player:BuffStack(S.NetherPrecisionBuff) == 1) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 24"; end
      end
      -- arcane_barrage,if=(active_enemies>1&talent.high_voltage&buff.arcane_charge.stack=4&buff.clearcasting.react&buff.aether_attunement.react&buff.glorious_incandescence.down&buff.intuition.down)
      if S.ArcaneBarrage:IsReady() and (EnemiesCount8ySplash > 1 and S.HighVoltage:IsAvailable() and Player:ArcaneCharges() == 4 and Player:BuffUp(S.ClearcastingBuff) and Player:BuffUp(S.AetherAttunementBuff) and Player:BuffDown(S.GloriousIncandescenceBuff) and Player:BuffDown(S.IntuitionBuff)) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 26"; end
      end
      -- arcane_barrage,if=(active_enemies>2&talent.orb_barrage&talent.high_voltage&debuff.magis_spark_arcane_blast.down&buff.arcane_charge.stack=4&target.health.pct<35&talent.arcane_bombardment&(buff.nether_precision.up|(buff.nether_precision.down&buff.clearcasting.react=0)))
      if S.ArcaneBarrage:IsReady() and (EnemiesCount8ySplash > 2 and S.OrbBarrage:IsAvailable() and S.HighVoltage:IsAvailable() and Target:DebuffDown(S.MagisSparkABDebuff) and Player:ArcaneCharges() == 4 and Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable() and (Player:BuffUp(S.NetherPrecisionBuff) or (Player:BuffDown(S.NetherPrecisionBuff) and Player:BuffDown(S.ClearcastingBuff)))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 28"; end
      end
      -- arcane_barrage,if=((active_enemies>2|(active_enemies>1&target.health.pct<35&talent.arcane_bombardment))&cooldown.arcane_orb.remains<gcd.max&buff.arcane_charge.stack=4&cooldown.touch_of_the_magi.remains>gcd.max*6&(debuff.magis_spark_arcane_blast.down|!talent.magis_spark)&buff.nether_precision.up&(talent.high_voltage|buff.nether_precision.stack=2|(buff.nether_precision.stack=1&buff.clearcasting.react=0)))
      if S.ArcaneBarrage:IsReady() and ((EnemiesCount8ySplash > 2 or (EnemiesCount8ySplash > 1 and Target:HealthPercentage() < 35 and S.ArcaneBombardment:IsAvailable())) and S.ArcaneOrb:CooldownRemains() < Player:GCD() and Player:ArcaneCharges() == 4 and S.TouchoftheMagi:CooldownRemains() > Player:GCD() * 6 and (Target:DebuffDown(S.MagisSparkABDebuff) or not S.MagisSpark:IsAvailable()) and Player:BuffUp(S.NetherPrecisionBuff) and (S.HighVoltage:IsAvailable() or Player:BuffStack(S.NetherPrecisionBuff) == 2 or (Player:BuffStack(S.NetherPrecisionBuff) == 1 and Player:BuffDown(S.ClearcastingBuff)))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 30"; end
      end
      -- arcane_missiles,if=buff.clearcasting.react&((talent.high_voltage&buff.arcane_charge.stack<4)|buff.nether_precision.down|(buff.clearcasting.react=3&(!talent.high_voltage|active_enemies=1))),interrupt_if=tick_time>gcd.remains&(buff.aether_attunement.react=0|(active_enemies>3&(!talent.time_loop|talent.resonance))),interrupt_immediate=1,interrupt_global=1,chain=1
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.ClearcastingBuff) and ((S.HighVoltage:IsAvailable() and Player:ArcaneCharges() < 4) or Player:BuffDown(S.NetherPrecisionBuff) or (Player:BuffStack(S.ClearcastingBuff) == 3 and (not S.HighVoltage:IsAvailable() or EnemiesCount8ySplash == 1)))) then
        LastSFAM = 3
        if Cast(S.ArcaneMissiles) then return "arcane_missiles sunfury 32"; end
      end
      -- arcane_barrage,if=(buff.arcane_charge.stack=4&active_enemies>1&active_enemies<5&buff.burden_of_power.up&((talent.high_voltage&buff.clearcasting.react)|buff.glorious_incandescence.up|buff.intuition.react|(cooldown.arcane_orb.remains<gcd.max|action.arcane_orb.charges>0)))&(!talent.consortiums_bauble|talent.high_voltage)
      if S.ArcaneBarrage:IsReady() and ((Player:ArcaneCharges() == 4 and EnemiesCount8ySplash > 1 and EnemiesCount8ySplash < 5 and Player:BuffUp(S.BurdenofPowerBuff) and ((S.HighVoltage:IsAvailable() and Player:BuffUp(S.ClearcastingBuff)) or Player:BuffUp(S.GloriousIncandescenceBuff) or Player:BuffUp(S.IntuitionBuff) or (S.ArcaneOrb:CooldownRemains() < Player:GCD() or S.ArcaneOrb:Charges() > 0))) and (not S.ConsortiumsBauble:IsAvailable() or S.HighVoltage:IsAvailable())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 34"; end
      end
      -- arcane_orb,if=buff.arcane_charge.stack<3
      if S.ArcaneOrb:IsReady() and (Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneOrb) then return "arcane_orb sunfury 36"; end
      end
      -- arcane_barrage,if=(buff.glorious_incandescence.up&(cooldown.touch_of_the_magi.remains>6|!talent.magis_spark))|buff.intuition.react
      if S.ArcaneBarrage:IsReady() and ((Player:BuffUp(S.GloriousIncandescenceBuff) and (S.TouchoftheMagi:CooldownRemains() > 6 or not S.MagisSpark:IsAvailable())) or Player:BuffUp(S.IntuitionBuff)) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 38"; end
      end
      -- presence_of_mind,if=(buff.arcane_charge.stack=3|buff.arcane_charge.stack=2)&active_enemies>=3
      if S.PresenceofMind:IsReady() and ((Player:ArcaneCharges() == 3 or Player:ArcaneCharges() == 2) and EnemiesCount8ySplash >= 3) then
        if Cast(S.PresenceofMind) then return "presence_of_mind sunfury 40"; end
      end
      -- arcane_explosion,if=buff.arcane_charge.stack<2&active_enemies>1
      if S.ArcaneExplosion:IsReady() and (Player:ArcaneCharges() < 2 and EnemiesCount8ySplash > 1) then
        if Cast(S.ArcaneExplosion) then return "arcane_explosion sunfury 42"; end
      end
      -- arcane_blast
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "arcane_blast sunfury 44"; end
      end
      -- arcane_barrage
      if S.ArcaneBarrage:IsReady() then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury 46"; end
      end
    end    

    local function customSunfury()
      ------------------------------------------------------------
      -- Arcane Barrage at end of Arcane Soul window
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
        and Player:BuffUp(S.ArcaneSoulBuff)
        and Player:BuffRemains(S.ArcaneSoulBuff) < Player:GCD()*1.25
      then
        if Cast(S.ArcaneBarrage) then
          return "Sunfury Barrage: Final GCD of Arcane Soul window"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Missiles with max Clearcasting stacks (with clip logic)
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
        and Player:BuffStack(S.ClearcastingBuff) == 3
      then
        if Cast(S.ArcaneMissiles) then
          return "Sunfury Missiles: Using at 3x Clearcasting stacks"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage during Arcane Soul window
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.ArcaneSoulBuff) then
        if Cast(S.ArcaneBarrage) then
          return "Sunfury Barrage: Consuming during Arcane Soul"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage to avoid losing Intuition/Tempo buffs
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) <= Player:GCD()*1.5) or
            (Player:BuffUp(S.ArcaneTempoBuff) and Player:BuffRemains(S.ArcaneTempoBuff) <= Player:GCD()*1.5)
        )
      then
        if Cast(S.ArcaneBarrage) then
          return "Sunfury Barrage: Preserving expiring buff (Intuition/Tempo)"
        end
      end
    
      ------------------------------------------------------------
      -- Shifting Power during cooldown downtime
      ------------------------------------------------------------
      if S.ShiftingPower:IsCastable(true) and (
        Player:BuffDown(S.ArcaneSurgeBuff) and 
        Player:BuffDown(S.SiphonStormBuff) and 
        Player:BuffDown(S.ArcaneSoulBuff) and
        Target:DebuffDown(S.TouchoftheMagiDebuff)
      ) then
        ShouldUseShiftingPower = true
      end
    
      ------------------------------------------------------------
      -- Arcane Missiles with Aether Attunement before TotM
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
        and Player:BuffUp(S.AetherAttunementBuff)
        and S.TouchoftheMagi:CooldownRemains() < Player:GCD() * 2
      then
        if Cast(S.ArcaneMissiles) then
          return "Sunfury Missiles: Aether Attunement before Touch of the Magi"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast with Leydrinker buff
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.LeydrinkerBuff) then
        if Cast(S.ArcaneBlast) then
          return "Sunfury Blast: Consuming Leydrinker buff"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (HV build logic)
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady()
        and S.HighVoltage:IsAvailable()
        and Player:BuffUp(S.ClearcastingBuff)
        and (not Player:BuffUp(S.NetherPrecisionBuff) or Player:ArcaneCharges() < 3)
      then
        if Cast(S.ArcaneMissiles) then
          return "Sunfury Missiles: Clearcasting with High Voltage build"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Orb to rebuild charges
      ------------------------------------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneOrb) then
          return "Sunfury Orb: Rebuilding charges (<3)"
        end
      end

      ------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (charge/precision logic)
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "ST Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady() and Player:BuffUp(S.ClearcastingBuff)
        and (not Player:BuffUp(S.NetherPrecisionBuff) or Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneMissiles) then
          return "ST Sunfury Missiles: Clearcasting without NP or low charges"
        end
      end

      ------------------------------------------------------------
      -- Arcane Barrage with proc buffs
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            Player:BuffUp(S.IntuitionBuff) or
            (Player:BuffUp(S.GloriousIncandescenceBuff) and S.TouchoftheMagi:CooldownRemains() >= 6)
        )
      then
        if Cast(S.ArcaneBarrage) then
          return "Sunfury Barrage: Utilizing Intuition/Glorious buff"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Explosion for charge building
      ------------------------------------------------------------
      if S.ArcaneExplosion:IsReady() and Player:ArcaneCharges() < 2 then
        if Cast(S.ArcaneExplosion) then
          return "Sunfury Explosion: Building charges (<2)"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast as main filler
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then
          return "Sunfury Blast: Primary filler spell"
        end
      end
    
      ------------------------------------------------------------
      -- Emergency mana conservation
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:ManaPercentage() < 10 then
        if Cast(S.ArcaneBarrage) then
          return "Sunfury Barrage: Emergency mana conservation (<10%)"
        end
      end
    end    
    
    local function SunfuryAoE()
      -- arcane_barrage,if=(buff.arcane_soul.up&((buff.clearcasting.react<3)|buff.arcane_soul.remains<gcd.max))
      if S.ArcaneBarrage:IsReady() and (Player:BuffUp(S.ArcaneSoulBuff) and ((Player:BuffStack(S.ClearcastingBuff) < 3) or Player:BuffRemains(S.ArcaneSoulBuff) < Player:GCD())) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury_aoe 2"; end
      end
      -- arcane_missiles,if=buff.arcane_soul.up,interrupt_if=tick_time>gcd.remains&buff.aether_attunement.down,interrupt_immediate=1,interrupt_global=1,chain=1
      if Player:IsChanneling(S.ArcaneMissiles) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and (Player:BuffDown(S.AetherAttunementBuff) or (EnemiesCount8ySplash > 3 and (not S.TimeLoop:IsAvailable() or S.Resonance:IsAvailable())))) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt sunfury_aoe 4"; end
      end
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.ArcaneSoulBuff)) then
        if Cast(S.ArcaneMissiles) then return "arcane_missiles sunfury_aoe 6"; end
      end
      -- shifting_power,if=(buff.arcane_surge.down&buff.siphon_storm.down&debuff.touch_of_the_magi.down&cooldown.evocation.remains>15&cooldown.touch_of_the_magi.remains>15)&(cooldown.arcane_orb.remains&action.arcane_orb.charges=0)&fight_remains>10
      if S.ShiftingPower:IsCastable(true) and ((Player:BuffDown(S.ArcaneSurgeBuff) and Player:BuffDown(S.SiphonStormBuff) and Target:DebuffDown(S.TouchoftheMagiDebuff) and S.Evocation:CooldownRemains() > 15 and S.TouchoftheMagi:CooldownRemains() > 15) and (S.ArcaneOrb:CooldownDown() and S.ArcaneOrb:Charges() == 0) and FightRemains > 10) then
        ShouldUseShiftingPower = true
      end
      -- arcane_orb,if=buff.arcane_charge.stack<2&(!talent.high_voltage|!buff.clearcasting.react)
      if S.ArcaneOrb:IsReady() and (Player:ArcaneCharges() < 2 and (not S.HighVoltage:IsAvailable() or Player:BuffDown(S.ClearcastingBuff))) then
        if Cast(S.ArcaneOrb) then return "arcane_orb sunfury_aoe 10"; end
      end
      -- arcane_blast,if=((debuff.magis_spark_arcane_blast.up|buff.burden_of_power.up|buff.leydrinker.up)&!prev_gcd.1.arcane_blast)
      if S.ArcaneBlast:IsReady() and ((Target:DebuffUp(S.MagisSparkABDebuff) or Player:BuffUp(S.BurdenofPowerBuff) or Player:BuffUp(S.LeydrinkerBuff)) and not Player:PrevGCDP(1, S.ArcaneBlast)) then
        if Cast(S.ArcaneBlast) then return "arcane_blast sunfury_aoe 12"; end
      end
      -- arcane_barrage,if=(buff.arcane_charge.stack=4|buff.glorious_incandescence.up|buff.aethervision.stack=2|buff.intuition.react)&(buff.nether_precision.up|buff.clearcasting.react=0)
      if S.ArcaneBarrage:IsReady() and ((Player:ArcaneCharges() == 4 or Player:BuffUp(S.GloriousIncandescenceBuff) or Player:BuffStack(S.AethervisionBuff) == 2 or Player:BuffUp(S.IntuitionBuff)) and (Player:BuffUp(S.NetherPrecisionBuff) or Player:BuffDown(S.ClearcastingBuff))) then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury_aoe 14"; end
      end
      -- arcane_missiles,if=buff.clearcasting.react&(buff.aether_attunement.react|talent.arcane_harmony),interrupt_if=tick_time>gcd.remains&buff.aether_attunement.down,interrupt_immediate=1,interrupt_global=1,chain=1
      if Player:IsChanneling(S.ArcaneMissiles) and (S.ArcaneMissiles:TickTime() > Player:GCDRemains() and Player:BuffDown(S.AetherAttunementBuff)) then
        if MainAddon.SetTopColor(1, "Stop Casting") then return "arcane_missiles interrupt sunfury_aoe 16"; end
      end
      if S.ArcaneMissiles:IsReady() and (Player:BuffUp(S.ClearcastingBuff) and (Player:BuffUp(S.AetherAttunementBuff) or S.ArcaneHarmony:IsAvailable())) then
        if Cast(S.ArcaneMissiles) then return "arcane_missiles sunfury_aoe 18"; end
      end
      -- presence_of_mind,if=buff.arcane_charge.stack=3|buff.arcane_charge.stack=2
      if S.PresenceofMind:IsReady() and (Player:ArcaneCharges() == 3 or Player:ArcaneCharges() == 2) then
        if Cast(S.PresenceofMind) then return "presence_of_mind sunfury_aoe 20"; end
      end
      -- arcane_explosion,if=talent.reverberate|buff.arcane_charge.stack<1
      if S.ArcaneExplosion:IsReady() and (S.Reverberate:IsAvailable() or Player:ArcaneCharges() < 1) then
        if Cast(S.ArcaneExplosion) then return "arcane_explosion sunfury_aoe 22"; end
      end
      -- arcane_blast
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then return "arcane_blast sunfury_aoe 24"; end
      end
      -- arcane_barrage
      if S.ArcaneBarrage:IsReady() then
        if Cast(S.ArcaneBarrage) then return "arcane_barrage sunfury_aoe 26"; end
      end
    end

    local function customSunfuryAoE()
      ------------------------------------------------------------
      -- Supernova at max Unerring Proficiency stacks
      ------------------------------------------------------------
      if S.Supernova:IsReady() and Player:BuffStack(S.UnerringProficiencyBuff) == 30 then
        if Cast(S.Supernova) then
          return "AoE Sunfury Supernova: Maximum Unerring stacks (30)"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast with Leydrinker buff
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.LeydrinkerBuff) then
        if Cast(S.ArcaneBlast) then
          return "AoE Sunfury Blast: Consuming Leydrinker buff"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast during TotM for Magi's Spark
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady()
        and Target:DebuffUp(S.TouchoftheMagiDebuff)
        and S.ArcaneBlast:TimeSinceLastCast() >= 10
      then
        if Cast(S.ArcaneBlast) then
          return "AoE Sunfury Blast: Triggering Magi's Spark during TotM"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage at end of Arcane Soul window
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
        and Player:BuffUp(S.ArcaneSoulBuff)
        and Player:BuffRemains(S.ArcaneSoulBuff) < Player:GCD()*1.25
      then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: Final GCD of Arcane Soul window"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Missiles with max Clearcasting stacks
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "AoE Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady() and Player:BuffStack(S.ClearcastingBuff) == 3 then
        if Cast(S.ArcaneMissiles) then
          return "AoE Sunfury Missiles: Using at 3x Clearcasting stacks"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage during Arcane Soul window
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:BuffUp(S.ArcaneSoulBuff) then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: Consuming during Arcane Soul"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage to avoid losing Intuition/Tempo buffs
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            (Player:BuffUp(S.IntuitionBuff) and Player:BuffRemains(S.IntuitionBuff) <= Player:GCD() * 1.5) or
            (Player:BuffUp(S.ArcaneTempoBuff) and Player:BuffRemains(S.ArcaneTempoBuff) <= Player:GCD() * 1.5)
        )
      then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: Preserving expiring buff (Intuition/Tempo)"
        end
      end
    
      ------------------------------------------------------------
      -- Shifting Power during cooldown downtime
      ------------------------------------------------------------
      if S.ShiftingPower:IsCastable(true) and (
        Player:BuffDown(S.ArcaneSurgeBuff) and 
        Player:BuffDown(S.SiphonStormBuff) and 
        Player:BuffDown(S.ArcaneSoulBuff) and
        Target:DebuffDown(S.TouchoftheMagiDebuff)
      ) then
        ShouldUseShiftingPower = true
      end
    
      ------------------------------------------------------------
      -- Arcane Missiles with Clearcasting (charge/precision logic)
      ------------------------------------------------------------
      -- Clip channel if needed
      if Player:IsChanneling(S.ArcaneMissiles)
        and not Player:IsMoving()
        and Player:BuffDown(S.AetherAttunementBuff)
        and S.ArcaneMissiles:TickTime() > Player:GCDRemains()
      then
        if MainAddon.SetTopColor(1, "Stop Casting") then
          return "AoE Sunfury Clipping: Stopping Missiles channel"
        end
      end
      
      if S.ArcaneMissiles:IsReady() and Player:BuffUp(S.ClearcastingBuff)
        and (not Player:BuffUp(S.NetherPrecisionBuff) or Player:ArcaneCharges() < 3) then
        if Cast(S.ArcaneMissiles) then
          return "AoE Sunfury Missiles: Clearcasting without NP or low charges"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Orb for charge building
      ------------------------------------------------------------
      if S.ArcaneOrb:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneOrb) then
          return "AoE Sunfury Orb: Rebuilding charges (<3)"
        end
      end
    
      -------------------------------------------------------------------
      -- Barrage with high Arcane Harmony stacks
      -------------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
          and Player:ArcaneCharges() == 4
          and Player:BuffStack(S.NetherPrecisionBuff) <= 1
          and ((S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 12)
            or (not S.HighVoltage:IsAvailable() and Player:BuffStack(S.ArcaneHarmonyBuff) > 18)) then
        if Cast(S.ArcaneBarrage) then return "AoE Sunfury Barrage: High Harmony stacks (12+/18+)" end
      end

      ------------------------------------------------------------
      -- Arcane Barrage at max charges during execute phase
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
        and Player:ArcaneCharges() == 4
        and Target:HealthPercentage() < 35
      then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: 4 charges during execute phase"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage at max charges with Orb ready
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady()
        and Player:ArcaneCharges() == 4
        and S.ArcaneOrb:CooldownDown()
      then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: 4 charges with Orb available"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Barrage with proc buffs
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and (
            Player:BuffUp(S.IntuitionBuff) or
            (Player:BuffUp(S.GloriousIncandescenceBuff) and S.TouchoftheMagi:CooldownRemains() >= 6)
        )
      then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: Utilizing Intuition/Glorious buff"
        end
      end
    
      ------------------------------------------------------------
      -- Presence of Mind for rapid charge building
      ------------------------------------------------------------
      if S.PresenceofMind:IsReady() and Player:ArcaneCharges() < 4 then
        if Cast(S.PresenceofMind) then
          return "AoE Sunfury PoM: Accelerating charge generation"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast during Presence of Mind
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady() and Player:BuffUp(S.PresenceofMindBuff) then
        if Cast(S.ArcaneBlast) then
          return "AoE Sunfury Blast: Using instant PoM procs"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Explosion for charge building
      ------------------------------------------------------------
      if S.ArcaneExplosion:IsReady() and Player:ArcaneCharges() < 3 then
        if Cast(S.ArcaneExplosion) then
          return "AoE Sunfury Explosion: Building charges (<3)"
        end
      end
    
      ------------------------------------------------------------
      -- Arcane Blast as main filler
      ------------------------------------------------------------
      if S.ArcaneBlast:IsReady() then
        if Cast(S.ArcaneBlast) then
          return "AoE Sunfury Blast: Primary filler spell"
        end
      end
    
      ------------------------------------------------------------
      -- Emergency mana conservation
      ------------------------------------------------------------
      if S.ArcaneBarrage:IsReady() and Player:ManaPercentage() < 10 then
        if Cast(S.ArcaneBarrage) then
          return "AoE Sunfury Barrage: Emergency mana conservation (<10%)"
        end
      end
    end    

    local function EvaluateTOTM(CycleUnit)
      return CycleUnit:DebuffUp(S.TouchoftheMagiDebuff) 
    end

    local function EvaluateBarrel(CycleUnit)
      return CycleUnit:NPCID() == 223724
    end

    --- ======= ACTION LISTS =======
    local function APL()
        -- Variables
        ArcaneCharges = Player:ArcaneCharges()

        if AoEON() then
            Enemies40y = Player:GetEnemiesInRangeCombat(40)
            Enemies8ySplash = Target:GetEnemiesInSplashRange(10)
        else
            Enemies40y = {Target}
            Enemies8ySplash = {Target}
        end
        EnemiesCount8ySplash = #Enemies8ySplash
        EnemiesCount40y = #Enemies40y

        if AoEON() and EnemiesCount8ySplash < 2 + num(S.Impetus:IsAvailable()) + num(S.SplinteringSorcery:IsAvailable()) then
          EnemiesCount8ySplash = EnemiesCount40y
        end

        local ReTarget = nil
        -- Re-Target ToTM
        if GetSetting('retarget_TOTM', 3) ~= 3 then
            if UnitWithTOTM(Enemies40y) > 0 then
                for i, ThisUnit in pairs(Enemies40y) do
                    if EvaluateTOTM(ThisUnit) then
                        MainAddon.Nameplate.AddIcon(ThisUnit, Spell(414379), true)
                        if not ReTarget then
                            ReTarget = GetTime() - 0.25
                        end
                    end
                end
                if GetSetting('retarget_TOTM', 3) == 1 then
                    if MouseOver:Exists() and MouseOver:DebuffUp(S.TouchoftheMagiDebuff) then
                        MainAddon.SetTopColor(1, "Target Mouseover")
                    end
                end
                if GetSetting('retarget_TOTM', 3) == 2 then
                    if ReTarget and ReTarget < GetTime() - 0.15 then
                        MainAddon.SetTopColor(1, "Target Enemy")
                        ReTarget = GetTime()
                    end  
                end
            else
                ReTarget = nil
            end
        end

        -- TotM on Arcane Surge cast
        if Player:AffectingCombat()
          and S.TouchoftheMagi:IsReady()
          and ((Player:BuffUp(S.ArcaneSurgeBuff) and S.ArcaneSurge:TimeSinceLastCast() <= 1.5) or Player:PrevGCDP(1, S.ArcaneSurge))
        then
          if Cast(S.TouchoftheMagi) then
            return "Surge -> TotM"
          end
        end


        if M.TargetIsValid() or Player:AffectingCombat() then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
              FightRemains = HL.FightRemains(Enemies8ySplash, false)
            end
        end

        GCDMax = Player:GCD() + 0.25

        if GetSetting('if', {})[2] and Player:IsCasting(S.Evocation) and not S.Slipstream:IsAvailable() and Player:BuffRemains(S.IceFloes) < 4.5 
        or GetSetting('if', {})[1] and Player:IsChanneling(S.ShiftingPower) and Player:BuffRemains(S.IceFloes) < 4.5 
        or GetSetting('if', {})[3] and Player:IsCasting(S.ArcaneSurge) and Player:BuffRemains(S.IceFloes) < 3
        then
            if S.IceFloes:IsReady(Player, nil, true, true) then
                if Cast(S.IceFloes, true, nil, nil, nil, true) then
                    return "Ice Floes"; 
                end
            end
        end

        -- Defensives
        ShouldReturn = Defensives()
        if ShouldReturn then
            return ShouldReturn
        end

        -- Utilities
        ShouldReturn = Utilities()
        if ShouldReturn then
            return ShouldReturn
        end

        if MainAddon.Toggle:GetToggle('ForceOpener') then
          if HL.CombatTime() < 4 then
                ShouldReturn = Opener()
                if ShouldReturn then
                    return ShouldReturn
                end
            else
                MainAddon.Toggle:SetToggle("ForceOpener", false)
            end
        end

        if HL.CombatTime() < 4 then
            -- call precombat
            ShouldReturn = Precombat()
            if ShouldReturn then
                return ShouldReturn
            end
        end

        if MainAddon.TargetIsValid() then
            if ShouldUseShiftingPower then
                if S.ShiftingPower:CooldownUp() and not S.ShiftingPower:IsBlocked()
                and (not Player:IsMoving() or S.IceFloes:IsAvailable() and not S.IceFloes:IsBlocked()) then
                    if Player:BuffUp(S.IceFloes) and S.ShiftingPower:IsReady(Player) and Player:BuffRemains(S.IceFloes) > 4 then
                        if Cast(S.ShiftingPower) then
                            return "Shifting Power x Ice Floes";
                        end
                    end
            
                    if GetSetting('if', {})[1] and S.IceFloes:IsReady(Player, nil, true, true) and Player:BuffRemains(S.IceFloes) < 4.5 then
                        if Cast(S.IceFloes, true, nil, nil, nil, true) then
                            return "Ice Floes x Shifting Power"; 
                        end
                    else
                        if S.ShiftingPower:IsReady(Player) then
                            if Cast(S.ShiftingPower) then
                                return "Shifting Power (normal)";
                            end
                        else
                          ShouldUseShiftingPower = false
                        end
                    end

                    return "Waiting for Ice Floes"
                else
                    if GetSetting('toast_SP', true) then
                        if S.ShiftingPower:IsBlocked() 
                        or Player:IsMoving() and not S.IceFloes:IsAvailable() 
                        or S.ShiftingPower:CooldownUp(nil, true) and not MainAddon.CDsON() then
                            MainAddon.UI:ShowToast("Fire Mage", "Shifting Power should be used now!", MainAddon.GetTexture(S.ShiftingPower), GetSetting('toast_SP_reset', 15))
                        end
                    end
                    ShouldUseShiftingPower = false
                end
            end
            
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end

            if S.Supernova:IsReady() and (not S.UnerringProficiency:IsAvailable() or Player:BuffStack(S.UnerringProficiencyBuff) == 30) then
              if CastCycle(S.Supernova, Enemies40y, EvaluateBarrel) then return "Supernova - Barrel"; end
            end

            -- lights_judgment,if=buff.arcane_surge.down&debuff.touch_of_the_magi.down&active_enemies>=2
            if S.LightsJudgment:IsReady() and (Player:BuffDown(S.ArcaneSurgeBuff) and Target:DebuffDown(S.TouchoftheMagiDebuff) and EnemiesCount8ySplash >= 2) then
              if Cast(S.LightsJudgment) then return "lights_judgment main 4"; end
            end
            if Player:PrevGCDP(1, S.ArcaneSurge) 
            or Player:BuffUp(S.ArcaneSurgeBuff) then
              -- custom add: touch_of_the_magi,if=prev_gcd.1.arcane_surge
              if S.TouchoftheMagi:IsReady() then
                if Cast(S.TouchoftheMagi) then return "touch_of_the_magi main 4.5"; end
              end
              -- berserking,if=prev_gcd.1.arcane_surge
              if S.Berserking:IsReady() then
                if Cast(S.Berserking) then return "berserking main 6"; end
              end
              -- blood_fury,if=prev_gcd.1.arcane_surge
              if S.BloodFury:IsReady() then
                if Cast(S.BloodFury) then return "blood_fury main 8"; end
              end
              -- fireblood,if=prev_gcd.1.arcane_surge
              if S.Fireblood:IsReady() then
                if Cast(S.Fireblood) then return "fireblood main 10"; end
              end
              -- ancestral_call,if=prev_gcd.1.arcane_surge
              if S.AncestralCall:IsReady() then
                if Cast(S.AncestralCall) then return "ancestral_call main 12"; end
              end
            end
            -- invoke_external_buff,name=power_infusion,if=(!equipped.spymasters_web&prev_gcd.1.arcane_surge)|(equipped.spymasters_web&prev_gcd.1.evocation)
            -- invoke_external_buff,name=blessing_of_summer,if=prev_gcd.1.arcane_surge
            -- invoke_external_buff,name=blessing_of_autumn,if=cooldown.touch_of_the_magi.remains>5
            -- Note: Not handling external buffs.
            -- use_items,if=((prev_gcd.1.arcane_surge&variable.steroid_trinket_equipped)|(cooldown.arcane_surge.ready&variable.steroid_trinket_equipped)|!variable.steroid_trinket_equipped&variable.nonsteroid_trinket_equipped|(variable.nonsteroid_trinket_equipped&buff.siphon_storm.remains<10&(cooldown.evocation.remains>17|trinket.cooldown.remains>20)))&!variable.spymasters_double_on_use|(fight_remains<20)
            if Player:AffectingCombat() and Target:IsInRange(40) then
              ---@class Item
              local ItemToUse, ItemSlot = Player:GetUseableItems(OnUseExcludes)
              if ItemToUse and (((Player:PrevGCDP(1, S.ArcaneSurge) and VarSteroidTrinketEquipped) or (S.ArcaneSurge:CooldownUp() and VarSteroidTrinketEquipped) or not VarSteroidTrinketEquipped and VarNonsteroidTrinketEquipped or (VarNonsteroidTrinketEquipped and Player:BuffRemains(S.SiphonStormBuff) < 10 and (S.Evocation:CooldownRemains() > 17 or ItemToUse:CooldownRemains() > 20))) and not VarSpymastersDoubleOnUse or (FightRemains < 20)) then
                if (ItemSlot == 13 or ItemSlot == 14) or (ItemSlot ~= 13 and ItemSlot ~= 14) then
                  if Cast(ItemToUse) then return "Generic use_items for " .. ItemToUse:Name() .. " main 14"; end
                end
              end
              -- use_item,name=treacherous_transmitter,if=buff.spymasters_report.stack<40
              if I.TreacherousTransmitter:IsEquippedAndReady() and (Player:BuffStack(S.SpymastersReportBuff) < 40) then
                if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter main 16"; end
              end
              -- use_item,name=high_speakers_accretion,if=(prev_gcd.1.arcane_surge|prev_gcd.1.evocation|(buff.siphon_storm.up&variable.opener)|cooldown.evocation.remains<4|fight_remains<20)&(buff.spymasters_report.stack<35)
              if I.HighSpeakersAccretion:IsEquippedAndReady() and ((Player:PrevGCDP(1, S.ArcaneSurge) or Player:PrevGCDP(1, S.Evocation) or (Player:BuffUp(S.SiphonStormBuff) and VarOpener) or S.Evocation:CooldownRemains() < 4 or BossFightRemains < 20) and (Player:BuffStack(S.SpymastersReportBuff) < 35)) then
                if Cast(I.HighSpeakersAccretion) then return "high_speakers_accretion main 20"; end
              end
              -- use_item,name=high_speakers_accretion,if=(prev_gcd.1.arcane_surge|prev_gcd.1.evocation)|cooldown.evocation.remains<4|fight_remains<20
              if I.HighSpeakersAccretion:IsEquippedAndReady() and ((Player:PrevGCDP(1, S.ArcaneSurge) or Player:PrevGCDP(1, S.Evocation)) or S.Evocation:CooldownRemains() < 4 or BossFightRemains < 20) then
                if Cast(I.HighSpeakersAccretion) then return "high_speakers_accretion main 20"; end
              end
              -- use_item,name=imperfect_ascendancy_serum,if=cooldown.evocation.ready|cooldown.arcane_surge.ready|fight_remains<20
              if I.ImperfectAscendancySerum:IsEquippedAndReady() and (S.Evocation:CooldownUp() or S.ArcaneSurge:CooldownUp() or BossFightRemains < 20) then
                if Cast(I.ImperfectAscendancySerum) then return "imperfect_ascendancy_serum main 22"; end
              end
              -- use_item,name=treacherous_transmitter,if=(cooldown.evocation.remains<7&cooldown.evocation.remains)|buff.siphon_storm.remains>15|fight_remains<20
              if I.TreacherousTransmitter:IsEquippedAndReady() and ((S.Evocation:CooldownRemains() < 7 and S.Evocation:CooldownDown()) or Player:BuffRemains(S.SiphonStormBuff) > 15 or BossFightRemains < 20) then
                if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter main 24"; end
              end
              -- do_treacherous_transmitter_task,use_off_gcd=1,if=buff.siphon_storm.up|fight_remains<20|(buff.cryptic_instructions.remains<?buff.realigning_nexus_convergence_divergence.remains<?buff.errant_manaforge_emission.remains)<3
              -- use_item,name=aberrant_spellforge,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down|(equipped.spymasters_web&target.health.pct>35)
              if I.AberrantSpellforge:IsEquippedAndReady() and (not VarSteroidTrinketEquipped or Player:BuffDown(S.SiphonStormBuff) or (I.SpymastersWeb:IsEquipped() and Target:HealthPercentage() > 35)) then
                if Cast(I.AberrantSpellforge) then return "aberrant_spellforge main 26"; end
              end
              -- use_item,name=mad_queens_mandate,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
              if I.MadQueensMandate:IsEquippedAndReady() and (not VarSteroidTrinketEquipped or Player:BuffDown(S.SiphonStormBuff)) then
                if Cast(I.MadQueensMandate) then return "mad_queens_mandate main 28"; end
              end
              -- use_item,name=fearbreakers_echo,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
              if I.FearbreakersEcho:IsEquippedAndReady() and (not VarSteroidTrinketEquipped or Player:BuffDown(S.SiphonStormBuff)) then
                if Cast(I.FearbreakersEcho) then return "fearbreakers_echo main 30"; end
              end
              -- use_item,name=mereldars_toll,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
              if I.MereldarsToll:IsEquippedAndReady() and (not VarSteroidTrinketEquipped or Player:BuffDown(S.SiphonStormBuff)) then
                if Cast(I.MereldarsToll) then return "mereldars_toll main 32"; end
              end
              -- use_item,name=neural_synapse_enhancer,if=(debuff.touch_of_the_magi.remains>8&buff.arcane_surge.up)|(debuff.touch_of_the_magi.remains>8&variable.neural_on_mini)
              if I.NeuralSynapseEnhancer:IsEquippedAndReady() then
                local shouldUse = false
                local reason = "neural_synapse_enhancer main 34"
                
                -- Check main condition: TotM debuff with more than 8 seconds left and Arcane Surge active
                if Target:DebuffRemains(S.TouchoftheMagiDebuff) > 8 and Player:BuffUp(S.ArcaneSurgeBuff) then
                  shouldUse = true
                  reason = "TotM+ArcaneSurge"
                -- Secondary condition: TotM debuff with more than 8 seconds left during mini-window
                elseif Target:DebuffRemains(S.TouchoftheMagiDebuff) > 8 and VarNeuralOnMini then
                  shouldUse = true
                  reason = "TotM+MiniWindow"
                end
                
                if shouldUse then
                  if Cast(I.NeuralSynapseEnhancer) then return reason; end
                end
              end
            end
            -- variable,name=opener,op=set,if=debuff.touch_of_the_magi.up&variable.opener,value=0
            -- Note: Added extra TotM checks so we don't get stuck in the opener if TotM is on CD or not talented.
            if (Target:DebuffUp(S.TouchoftheMagiDebuff) or S.TouchoftheMagi:CooldownRemains() > GCDMax * 4 or not S.TouchoftheMagi:IsAvailable()) and VarOpener then
              VarOpener = false
            end
            -- arcane_barrage,if=fight_remains<2
            if S.ArcaneBarrage:IsReady() and (FightRemains < 2 and not Player:InPvP() and not GetSetting("rotation_mode") == "APLcustom") then
              if Cast(S.ArcaneBarrage) then return "arcane_barrage main 36"; end
            end
            -- call_action_list,name=cd_opener
            ShouldReturn = CDOpener(); if ShouldReturn then return ShouldReturn; end
            -- call_action_list,name=sunfury_aoe,if=talent.spellfire_spheres&variable.aoe_list
            if AoEON() and (S.SpellfireSpheres:IsAvailable() and EnemiesCount8ySplash >= 3) then
              local ShouldReturn
              if GetSetting("rotation_mode") == "APLcustom" then
                ShouldReturn = customSunfuryAoE()
              else
                ShouldReturn = SunfuryAoE()
              end
              if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=spellslinger_aoe,if=!talent.spellfire_spheres&variable.aoe_list
            if AoEON() and (not S.SpellfireSpheres:IsAvailable() and EnemiesCount8ySplash >= 3) then
              local ShouldReturn
              if GetSetting("rotation_mode") == "APLcustom" then
                ShouldReturn = customSpellslingerAoE()
              else
                ShouldReturn = SpellslingerAoE()
              end
              if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=sunfury,if=talent.spellfire_spheres
            if S.SpellfireSpheres:IsAvailable() then
              local ShouldReturn
              if GetSetting("rotation_mode") == "APLcustom" then
                ShouldReturn = customSunfury()
              else
                ShouldReturn = Sunfury()
              end
              if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=spellslinger,if=!talent.spellfire_spheres
            if not S.SpellfireSpheres:IsAvailable() then
              local ShouldReturn
              if GetSetting("rotation_mode") == "APLcustom" then
                ShouldReturn = customSpellslinger()
              else
                ShouldReturn = Spellslinger()
              end
              if ShouldReturn then return ShouldReturn; end
            end
            -- arcane_barrage
            if S.ArcaneBarrage:IsReady() and not GetSetting("rotation_mode") == "APLcustom" then
              if Cast(S.ArcaneBarrage) then return "arcane_barrage main 38"; end
            end
        end
    end

    local function Init()
    end
    M.SetAPL(62, APL, Init)

    -- Arcane, ID: 62
    local ArcaneOldPlayerAffectingCombat
    ArcaneOldPlayerAffectingCombat =
    HL.AddCoreOverride("Player.AffectingCombat",
        function(self)
            if MainAddon.PlayerSpecID() == 62 then
                return Player:IsCasting(S.ArcaneBlast) or ArcaneOldPlayerAffectingCombat(self)
            end
            return ArcaneOldPlayerAffectingCombat(self)
        end,
    62)
    
    local ArcanePlayerBuffUp
    ArcanePlayerBuffUp = HL.AddCoreOverride("Player.BuffUp",
      function (self, Spell, AnyCaster, Offset)
        local BaseCheck = ArcanePlayerBuffUp(self, Spell, AnyCaster, Offset)
        if MainAddon.PlayerSpecID() == 62 then
          if Spell == S.ArcaneSurgeBuff then
            return BaseCheck or Player:IsCasting(S.ArcaneSurge)
          end
        end
        return BaseCheck
      end
    , 62)
    
    local ArcanePlayerBuffDown
    ArcanePlayerBuffDown = HL.AddCoreOverride("Player.BuffDown",
      function (self, Spell, AnyCaster, Offset)
        local BaseCheck = ArcanePlayerBuffDown(self, Spell, AnyCaster, Offset)
        if MainAddon.PlayerSpecID() == 62 then
          if Spell == S.ArcaneSurgeBuff then
            return BaseCheck and not Player:IsCasting(S.ArcaneSurge)
          end
        end
        return BaseCheck
      end
    , 62)

    local ArcaneOldSpellIsReady
    ArcaneOldSpellIsReady = HL.AddCoreOverride("Spell.IsReady",
        function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            if GetSetting('arcanesurge_nodispel', true) then
                if (self == S.PrismaticBarrier or self == S.MassBarrier or self == S.Supernova or self == S.DragonsBreath) then
                    if Player:BuffUp(S.ArcaneSurgeBuff) then
                        return false, "Disabled due to Arcane Surge"
                    end
                end

                if self == S.TouchoftheMagi then
                  if Player:BuffUp(S.ArcaneSurgeBuff) and (Player:BuffRemains(S.ArcaneSurgeBuff) > 1 and Player:BuffRemains(S.ArcaneSurgeBuff) <= 8) then
                      return false, "Disabled to make sure we don't use TotM with procced Arcane Surge"
                    end
                end
            end

            local BaseCheck = ArcaneOldSpellIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            return BaseCheck
        end
    , 62)
    
    local OldIsCastable
    OldIsCastable =
    HL.AddCoreOverride("Spell.IsCastable",
      function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources)
          local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources)
          if MainAddon.PlayerSpecID() == 62 then
            ignoreMovement = false

            if Player:BuffUp(S.IceFloes) then
                ignoreMovement = true
            end

            -- Arcane Missiles with Slipstream
            if self == S.ArcaneMissiles then
                if S.Slipstream:IsAvailable() then
                  ignoreMovement = true
                end
            end

            if self == S.ShiftingPower then
                if Player:IsStandingStillFor() < GetSetting("SPMovingValue", 1.5) and Player:BuffRemains(S.IceFloes) < 4 then
                    return false, "Is Not Standing Still"
                end

                if S.ArcaneSurge:CooldownUp(nil, true) or S.TouchoftheMagi:CooldownUp(nil, true) then
                  return false, "Arcane Surge or TotM is still ready"
                end
            end

            if self == S.ArcaneOrb then
                if Player:IsTurning() and not GetSetting("arcaneorb_when_turning", true) then
                    return false, "Player is turning"
                end

                -- Block during Kyrioss (Rookery)
                if M.CurrentEncounter == 2816 then
                  return false, "Special logic - Kyrioss (Rookery)"
                end
            end

            -- Nether Precision
            if self == S.ArcaneMissiles then
                if Player:BuffUp(S.NetherPrecisionBuff) then
                    return false, "Don't use Arcane Missiles with Nether Precision"
                end
            end

            if self == S.ArcaneBarrage then
              -- //NOTE - Cast arcane barrage with 4 arcane charges IF: We have an orb available to generate up to 4 charges immediately. We have AA (aether attunement) proc sitting on our hands (suggested by freelancefobia)
                if not GetSetting("rotation_mode") == "APLcustom" and Player:IsMoving() then
                    if Player:ArcaneCharges() == 4 and (S.ArcaneOrb:CooldownDown() and Player:BuffDown(S.AetherAttunementBuff)) then
                      return false, "Not worth"
                    end
                end
            end

            -- Block TotM if Arcane Surge is still ready
            if self == S.TouchoftheMagi then
                if S.ArcaneSurge:CooldownUp(nil, true) then
                    return false, "Arcane Surge is still ready"
                end
            end
            
            if self == S.GreaterInvisibility or self == S.Invisibility then
                if not MainAddon.safeVanish() then
                    return false, "Not safe to vanish"
                end
            end

            if self == S.PresenceofMind then
                return BaseCheck and Player:BuffDown(S.PresenceofMind)
            elseif self == S.ShiftingPower then
                return BaseCheck and not Player:IsCasting(self)
            elseif self == S.TouchoftheMagi then
                return BaseCheck and not Player:IsCasting(self)
            elseif self == S.ArcaneSurge then
                if self:WillBeInterruptByMechanic() then
                    return false, "Interrupted by Mechanic"
                end

                if not Player:IsMoving() or Player:BuffRemains(S.IceFloes) > 1.5 then
                    -- Allow casting ArcaneSurge even when the proc-based short buff is active
                    if Player:BuffUp(S.ArcaneSurgeBuff) and Player:BuffRemains(S.ArcaneSurgeBuff) > 0.15 and Player:BuffRemains(S.ArcaneSurgeBuff) <= 8 then
                        return self:IsLearned() and self:CooldownUp() and not Player:IsCasting(self)
                    else
                        return self:IsLearned() and self:CooldownUp() and not Player:IsCasting(self) and Player:BuffDown(S.ArcaneSurgeBuff)
                    end
                end
            end
          end
          BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources)
          return BaseCheck, Reason
      end,
    62)

    local ArcaneChargesPowerType = Enum.PowerType.ArcaneCharges
    local ArcaneOldPlayerArcaneCharges
    ArcaneOldPlayerArcaneCharges = HL.AddCoreOverride("Player.ArcaneCharges",
      function (self)
        local BaseCharges = UnitPower("player", ArcaneChargesPowerType)
        if Player:IsCasting(S.ArcaneBlast) then
          return mathmin(BaseCharges + 1, 4)
        else
          return BaseCharges
        end
      end
    , 62)

    local OldIsCastableQueue
    OldIsCastableQueue = HL.AddCoreOverride("Spell.IsCastableQueue",
            function(self, ignoreMovement)
              if MainAddon.PlayerSpecID() == 62 then
                if Player:BuffUp(S.IceFloes) then
                    ignoreMovement = true
                end
              end
              local BaseCheck, Reason = OldIsCastableQueue(self, ignoreMovement)
              return BaseCheck, Reason
        end,
    62);
    
    HL:RegisterForEvent(
      function()
          -- Update Evocation
          if S.Slipstream:IsAvailable() then
              MainAddon.CONST.SpellIsChannel[S.Evocation:ID()].StandStill = false
          else
              MainAddon.CONST.SpellIsChannel[S.Evocation:ID()].StandStill = true
          end
      end, "PLAYER_ENTERING_WORLD", "UPDATE_CHAT_WINDOWS", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB"
    )  
end