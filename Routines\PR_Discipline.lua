function A_256(...)
    -- Core References
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Unit
    local Pet = Unit.Pet
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastCycleAlly = M.CastCycleAlly
    local CastTargetIfAlly = M.CastTargetIfAlly
    local CastCycle = M.CastCycle
    local CastAlly = M.CastAlly
    local CastMagicAlly = M.CastMagicAlly
    local CastMagic = M.CastMagic
    ---@class Priest
    local Priest = M.Priest
    -- Lua
    local AoEON = M.AoEON
    local IsInGroup = _G['IsInGroup']
    local GetTime = _G['GetTime']
    local GetNumGroupMembers = _G['GetNumGroupMembers']
    local num = M.num
    
    -- Define spell and item references
    local S = Spell.Priest.Discipline
    local I = Item.Priest.Discipline

    -- Items to exclude from OnUse function
    local OnUseExcludes = {
        I.IridaltheEarthsMaster:ID(),
        -- I.Dreambinder:ID(),
    }

    -- Toggle Settings
    MainAddon.Toggle.Special["SpreadAtonement"] = {
        Icon = MainAddon.GetTexture(S.Atonement),
        Name = "Atonement",
        Description = "Spread Atonement.",
        Spec = 256
    }

    MainAddon.Toggle.Special["Ramp"] = {
        Icon = MainAddon.GetTexture(S.HolyNova),
        Name = "Ramp",
        Description = "Ramp rotation.",
        Spec = 256
    }

    MainAddon.Toggle.Special["ForceDPS"] = {
        Icon = MainAddon.GetTexture(S.Smite),
        Name = "Force DPS",
        Description = "This toggle will force DPS.",
        Spec = 256,
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FFFFFF'
    local Config_Table = {
        key = Config_Key,
        title = 'Priest - Discipline',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = "header", text = "\"In the light, purity finds its eternal sanctuary.\"", size = 16, align = "center", color = Config_Color },
            { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            
            { type = 'header', text = 'Single Target Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Evangelism
            { type = 'header', text = 'Evangelism', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'EVANHP', icon = S.Evangelism:ID(), min = 1, max = 100, default = 38},
            { type = 'spacer' },
            
            -- Flash Heal
            { type = 'header', text = 'Flash Heal', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Hardcast Threshold (%)', key = 'FlashHealHP', icon = S.FlashHeal:ID(), min = 1, max = 100, default = 50 },
            { type = 'spinner', text = 'Instant Threshold (%)', key = 'FlashHealHP_Surge', icon = S.FlashHeal:ID(), min = 1, max = 100, default = 82 },
            { type = 'spinner', text = 'Emergency Threshold (%)', key = 'FlashHealHP_EH', icon = S.FlashHeal:ID(), min = 1, max = 100, default = 35 },
            { type = 'spacer' },
            
            -- Penance
            { type = 'header', text = 'Penance', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Emergency Threshold (%)', key = 'PenanceHP_EH', icon = S.Penance:ID(), min = 1, max = 100, default = 45 },
            { type = 'spacer' },
            
            -- Power Word: Shield
            { type = 'header', text = 'Power Word: Shield', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'PWSHP', icon = S.PowerWordShield:ID(), min = 1, max = 100, default = 90},
            { type = 'spinner', text = 'Tank Threshold - Oracle (%)', key = 'PWSTANKHP', icon = S.PowerWordShield:ID(), min = 1, max = 100, default = 80},
            { type = 'spacer' },
            
            -- Renew
            { type = 'header', text = 'Renew', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'RenewHP', icon = S.Renew:ID(), min = 1, max = 100, default = 50},
            { type = 'checkbox', text = 'Only Cast While Moving', key = 'renewMovement', icon = S.Renew:ID(), default = true },
            { type = 'spacer' },
            
            { type = 'header', text = 'Group Healing', color = Config_Color },
            { type = 'spacer' },

            -- Pet
            { type = 'text', text = "Pet:" },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PET_underX', icon = S.Mindbender:ID(), min = 1, max = 100, default = 40},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PET_underX_val', icon = S.Mindbender:ID(), min = 1, max = 100, default = 75},
            { type = 'spacer' },
            
            -- Evangelism (Group)
            { type = 'header', text = 'Evangelism', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'EVAN_underX', icon = S.Evangelism:ID(), min = 1, max = 100, default = 60},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'EVAN_underX_val', icon = S.Evangelism:ID(), min = 1, max = 100, default = 75},
            { type = 'spinner', text = 'Atonement Value (%)', key = 'EVAN_Atonement_val', icon = S.Evangelism:ID(), min = 1, max = 100, default = 75},
            { type = 'spacer' },
            
            -- Power Word: Radiance
            { type = 'header', text = 'Power Word: Radiance', size = 14, align = 'Left', color = Config_Color },
            
            -- Atonement Spread
            { type = 'text', text = "Atonement Spread:" },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PWR_underX', icon = S.PowerWordRadiance:ID(), min = 1, max = 100, default = 40},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PWR_underX_val', icon = S.PowerWordRadiance:ID(), min = 1, max = 100, default = 85},
            
            -- Emergency Healing
            { type = 'text', text = "Emergency Healing:" },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PWR_EH_underX', icon = S.PowerWordRadiance:ID(), min = 1, max = 100, default = 80},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PWR_EH_underX_val', icon = S.PowerWordRadiance:ID(), min = 1, max = 100, default = 50},
            { type = 'checkbox', text = 'Keep One Charge For Manual Use', key = 'keepradiance', icon = S.PowerWordRadiance:ID(), default = false },
            { type = 'spacer' },
            
            -- Entropic Rift
            { type = 'header', text = 'Entropic Rift', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'ER_underX', icon = S.EntropicRift:ID(), min = 1, max = 100, default = 40},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'ER_underX_val', icon = S.EntropicRift:ID(), min = 1, max = 100, default = 80},
            { type = 'spacer' },
            
            -- Power Word: Barrier
            { type = 'header', text = 'Power Word: Barrier', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PWBar_underX', icon = S.PowerWordBarrier:ID(), min = 1, max = 100, default = 80},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PWBar_underX_val', icon = S.PowerWordBarrier:ID(), min = 1, max = 100, default = 58},
            { type = 'dropdown', text = 'Magic Groundspell Placement', key = 'magicgroundspell', icon = S.PowerWordBarrier:ID(),
                list = {
                    { text = 'Friend', key = 1 },
                    { text = 'Enemy', key = 2 },
                    { text = 'No Magic', key = 3 },
                },
                default = 3
            },
            { type = 'spacer' },
            
            -- Ultimate Penitence
            { type = 'header', text = 'Ultimate Penitence', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'UP_underX', icon = S.UltimatePenitence:ID(), min = 1, max = 100, default = 60},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'UP_underX_val', icon = S.UltimatePenitence:ID(), min = 1, max = 100, default = 50},
            { type = 'spinner', text = 'Atonement Value (%)', key = 'UP_Atonement_val', icon = S.UltimatePenitence:ID(), min = 1, max = 100, default = 65},
            { type = 'spacer' },
            
            -- Vampiric Embrace
            { type = 'header', text = 'Vampiric Embrace', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'VE_groupHP', icon = S.VampiricEmbrace:ID(), min = 1, max = 100, default = 80 },
            { type = 'spacer' },
            
            { type = 'header', text = 'Premonitions', color = Config_Color },
            { type = 'spacer' },
            
            -- Premonition Health Thresholds
            { type = 'header', text = 'Health Thresholds', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Clairvoyance (Avg Group Health)', key = 'PremonitionOfClairvoyance', icon = S.PremonitionOfClairvoyance:ID(), min = 1, max = 100, default = 50 },
            { type = 'spinner', text = 'Insight (Avg Group Health)', key = 'PremonitionOfInsight', icon = S.PremonitionOfInsight:ID(), min = 1, max = 100, default = 78 },
            { type = 'spinner', text = 'Piety (Avg Group Health)', key = 'PremonitionOfPiety', icon = S.PremonitionOfPiety:ID(), min = 1, max = 100, default = 70 },
            { type = 'spinner', text = 'Solace (Lowest Member Health)', key = 'PremonitionOfSolace', icon = S.PremonitionOfSolace:ID(), min = 1, max = 100, default = 68 },
            { type = 'spacer' },
            
            -- Premonition Handling
            { type = 'header', text = 'Premonition Behaviors', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Insight Handling', key = 'PoIusage', icon = S.PremonitionOfInsight:ID(),
                list = {
                  { text = 'Full Auto', key = 'PoIauto' },
                  { text = 'Penance Spam', key = 'PoIPenance' },
                  { text = 'Weal and Woe', key = 'PoIWW' },
                },
                default = 'PoIauto',
            },
            { type = 'dropdown', text = 'Solace Handling', key = 'PoSusage', icon = S.PremonitionOfSolace:ID(),
                list = {
                  { text = 'Full Auto', key = 'PoSauto' },
                  { text = 'Flash Heal', key = 'PoSFlashHeal' },
                  { text = 'Penance', key = 'PoSPenance' },
                },
                default = 'PoSauto',
            },
            { type = 'checkbox', text = 'Combine Insight and Piety In Danger', key = 'InsightPietyCombo', default = true },
            { type = 'spacer' },
            
            { type = 'header', text = 'External Cooldowns', color = Config_Color },
            { type = 'spacer' },
            
            -- Pain Suppression
            { type = 'header', text = 'Pain Suppression', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Tank Health Threshold (%)', key = 'PSTanksHP', icon = S.PainSuppression:ID(), min = 1, max = 100, default = 35},
            { type = 'checkspin', text = 'Group Member Threshold (%)', key = 'PSMembersHP', icon = S.PainSuppression:ID(), min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'spacer' },
            
            -- Void Shift
            { type = 'header', text = 'Void Shift', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Target Health Threshold (%)', key = 'VoidShift_Members', icon = S.VoidShift:ID(), min = 1, max = 100, default_spin = 38, default_check = true },
            { type = 'checkspin', text = 'Self Health Threshold (%)', key = 'VoidShift_Self', icon = S.VoidShift:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'spacer' },
            
            -- Power Infusion
            { type = 'header', text = 'Power Infusion', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Full Auto Mode', key = 'pi_fullauto', icon = S.PowerInfusion:ID(), default = true },
            { type = 'checkbox', text = 'Show Toast Notification', key = 'pi_fullauto_toast', icon = S.PowerInfusion:ID(), default = true },
            { type = 'spacer' },
            
            { type = 'header', text = 'Personal Defensives', color = Config_Color },
            { type = 'spacer' },
            
            -- Desperate Prayer
            { type = 'header', text = 'Desperate Prayer', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'DesperatePrayerHP', icon = S.DesperatePrayer:ID(), min = 1, max = 100, default = 35 },
            { type = 'spacer' },
            
            { type = 'header', text = 'Movement & Utility', color = Config_Color },
            { type = 'spacer' },
            
            -- Movement Options
            { type = 'header', text = 'Movement Abilities', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Angelic Feather (sec)', key = 'angelic', icon = S.AngelicFeather:ID(), min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'checkspin', text = 'Body and Soul (sec)', key = 'bsoul', icon = S.BodyAndSoul:ID(), min = 0, max = 15, default_spin = 2, default_check = false },
            { type = 'text', text = "      PW:S will only be used for Body and Soul while not in a Dungeon or Raid.", size = 12, color = '5D5D5D' },
            { type = 'spacer' },
            
            { type = 'header', text = 'Options and Extras', color = Config_Color },
            { type = 'spacer' },
            
            -- Pet Management
            { type = 'header', text = 'Pet Management', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Sync Pet with Radiance', key = 'bender_x_radiance', icon = S.Mindbender:ID(), default = true },
            { type = 'checkbox', text = 'Sync Pet with Premonition', key = 'bender_x_premonition', icon = S.Mindbender:ID(), default = true },
            { type = 'spinner', text = 'Use Pet Below Mana (%) (only when sync is unchecked) ', key = 'ShadowfiendMana', icon = S.Mindbender:ID(), min = 1, max = 100, default = 50 },
            { type = 'spacer' },
            
            -- Atonement Options
            { type = 'header', text = 'Atonement Options', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Spread Atonement With:', key = 'SA', icon = S.Atonement:ID(), multiselect = true, 
                list = { 
                    { text = 'Shield', key = 1 }, 
                    { text = 'Renew', key = 2 } 
                }, 
                default = { 1, 2 } 
            },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'ruler' },
            { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Discipline", Config_Color)
    MainAddon.SetConfig(256, Config_Table)

    -- Vars
    local Tanks, Healers, Members, Damagers, Melees, MembersWithoutTanks, TargetIfAlly
    Priest.MembersPI = {}
    Priest.MembersPIAuto = {}
    local Enemies12y = {}
    local EnemiesCount12y = 0
    local Enemies40y = {}
    local EnemiesCount40y = 0
    local BossFightRemains = 11111
    local FightRemains = 11111
    local DivineStar = S.DivineStar
    local Halo = S.Halo
    local Penance = S.Penance
    local Settings = {}
    local Var = {}
    Var['MembersWithAtonementHealthAverage'] = 100
    Var['AtonementAverage'] = 0
    Var['CombatMonitor_TimeStamp'] = GetTime()
    Var['CombatMonitor_State'] = false
    Var['Fiend'] = (S.MindbenderTalent:IsAvailable() and S.Mindbender) or (S.VoidWraith:IsAvailable() and S.VoidWraithAbility) or S.Shadowfiend

    -- Update Fiend reference when spells change
    HL:RegisterForEvent(function()
        Var['Fiend'] = (S.MindbenderTalent:IsAvailable() and S.Mindbender) or (S.VoidWraith:IsAvailable() and S.VoidWraithAbility) or S.Shadowfiend
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    -- Monitor if the group is in combat by checking tank status
    local function combatMonitor()
        -- Return cached value if we checked recently
        if GetTime() - Var['CombatMonitor_TimeStamp'] < 1 then
            return Var['CombatMonitor_State']
        end
        
        -- Check if any tank is in combat
        if Tanks then
            ---@param TankUnit Unit
            for _, TankUnit in pairs(Tanks) do
                if TankUnit:AffectingCombat() then
                    Var['CombatMonitor_TimeStamp'] = GetTime()
                    Var['CombatMonitor_State'] = true
                    return true
                end
            end
        end

        -- No tanks in combat
        Var['CombatMonitor_TimeStamp'] = GetTime()
        Var['CombatMonitor_State'] = false
        return false
    end

    -- Reset fight timers when leaving combat
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    -- Functions
    local function UpdateVars()
        -- Group composition
        Tanks, Healers, Members, Damagers, Melees, _, _, MembersWithoutTanks, TargetIfAlly = HealingEngine:Fetch()
        
        -- Enemy tracking
        if AoEON() then
            Enemies12y = Player:GetEnemiesInRange(8)
            Enemies40y = Player:GetEnemiesInRange(40)
        else
            Enemies12y = {Target}
            Enemies40y = {Target}
        end
        EnemiesCount12y = #Enemies12y
        EnemiesCount40y = #Enemies40y
        
        if Members and #Members > 0 then
            -- Create copies to avoid modifying the original HealingEngine tables
            Priest.MembersPI = {}
            Priest.MembersPIAuto = {}
            
            -- Copy Members table
            for i = 1, #Members do
                Priest.MembersPI[i] = Members[i]
            end
            
            -- Copy Damagers table
            if Damagers then
                for i = 1, #Damagers do
                    Priest.MembersPIAuto[i] = Damagers[i]
                end
            end
            
            Priest.SortMembersPI(Priest.MembersPI, Enemies40y and #Enemies40y or 0)
        end
        
        -- Update spell references based on Shadow Covenant
        if Player:BuffUp(S.ShadowCovenantBuff) then
            Halo = S.ShadowHalo
            Penance = S.ShadowPenance
            DivineStar = S.ShadowDivineStar
        else
            Halo = S.Halo
            Penance = S.Penance
            DivineStar = S.DivineStar
        end

        -- Core state variables
        Var['AverageHPInRange'] = HealingEngine:MedianHP()
        Var['AngelicFeatherBuff'] = Player:BuffUp(S.AngelicFeatherBuff)
        Var['BodyAndSoulBuff'] = Player:BuffUp(S.BodyAndSoulBuff)
        Var['Shadowmeld'] = Player:BuffUp(S.Shadowmeld)
        Var['Fade'] = Player:BuffUp(S.Fade)
        Var['PlayerGCD'] = Player:GCD()
        Var['TargetIsValid'] = M.TargetIsValid()
        Var['TargetIsValidAndImmune'] = M.TargetIsValid(nil, 3)
        Var['IsInCombat'] = Player:AffectingCombat()
        Var['ManaPct'] = Player:ManaPercentage()
        Var['InDungeon'] = Player:IsInDungeonArea()
        Var['HarshDisciplineBuff'] = Player:BuffUp(S.HarshDisciplineBuff)
        Var['IsMoving'] = Player:IsMoving()
        Var['BuffAtonementInGroup'] = HealingEngine:BuffTotal(S.Atonement, 40)
        Var['PenanceCooldownRemains'] = Penance:CooldownRemains()
        Var['partySize'] = GetNumGroupMembers()
        Var['MembersAmount'] = Var['partySize'] < 5 and 5 or Var['partySize']
        Var['MembersWithAtonement'] = {}
        Var['MembersWithoutAtonement'] = {}
        Var['MembersWithoutAtonementCritical'] = {}
        Var['LowestHP'], Var['LowestUnit'] = HealingEngine:LowestHP(true, 40)
        Var['AtonementPercentage'] = Var['BuffAtonementInGroup'] / Var['MembersAmount'] * 100
        
        -- Analyze Atonement status on group members
        if Members and #Members > 0 then
            local totalHealth = 0
            local atonementCount = 0
            
            for i = 1, #Members do
                if Members[i]:BuffUp(S.Atonement) then
                    -- Track members with Atonement
                    table.insert(Var['MembersWithAtonement'], Members[i])
                    totalHealth = totalHealth + Members[i]:HealthPercentage()
                    atonementCount = atonementCount + 1
                else
                    -- Track members without Atonement
                    table.insert(Var['MembersWithoutAtonement'], Members[i])
                    local healthPct = Members[i]:HealthPercentage()
                    if healthPct <= 95 then
                        table.insert(Var['MembersWithoutAtonementCritical'], Members[i])
                    end
                end
            end
            
            -- Calculate average health of members with Atonement
            if atonementCount > 0 then
                Var['MembersWithAtonementHealthAverage'] = totalHealth / atonementCount
            end
            
            -- Calculate average Atonement duration remaining
            if #Var['MembersWithAtonement'] > 0 then
                local totalDuration = 0
                for i = 1, #Var['MembersWithAtonement'] do
                    local unit = Var['MembersWithAtonement'][i]
                    if unit then
                        totalDuration = totalDuration + unit:BuffRemains(S.Atonement)
                    end
                end
                Var['AtonementAverage'] = totalDuration / #Var['MembersWithAtonement']
            end
        end

        -- Inescapable Torment talent: enable aggressive Penance usage when pet is active
        Var['AggressivePenance'] = S.InescapableTorment:IsAvailable() and Var['PetActive']
        Var['PetActive'] = Player:TotemIsActive(136199) or Player:TotemIsActive(136214) or Player:TotemIsActive(615099)
        -- 136199: Shadowfiend; 136214: Mindbender; 615099: Voidwrath

        -- Load settings for percentage-based thresholds
        if Var['MembersAmount'] then
            -- Convert percentage settings to actual member counts
            Settings['PWR_underX'] = (GetSetting("PWR_underX", 40) * Var['MembersAmount']) / 100
            Settings['PWR_underX_val'] = GetSetting("PWR_underX_val", 85)
            Settings['PWR_EH_underX'] = (GetSetting("PWR_EH_underX", 80) * Var['MembersAmount']) / 100
            Settings['PWR_EH_underX_val'] = GetSetting("PWR_EH_underX_val", 50)
            Settings['UP_underX'] = (GetSetting("UP_underX", 60) * Var['MembersAmount']) / 100
            Settings['UP_underX_val'] = GetSetting("UP_underX_val", 50)
            Settings['UP_Atonement_val'] = GetSetting("UP_Atonement_val", 65)
            Settings['EVAN_underX'] = (GetSetting("EVAN_underX", 60) * Var['MembersAmount']) / 100
            Settings['EVAN_underX_val'] = GetSetting("EVAN_underX_val", 75)
            Settings['EVAN_Atonement_val'] = GetSetting("EVAN_Atonement_val", 75)
            Settings['ER_underX'] = (GetSetting("ER_underX", 40) * Var['MembersAmount']) / 100
            Settings['ER_underX_val'] = GetSetting("ER_underX_val", 80)
            Settings['PWBar_underX'] = (GetSetting("PWBar_underX", 80) * Var['MembersAmount']) / 100
            Settings['PWBar_underX_val'] = GetSetting("PWBar_underX_val", 58)
            Settings['PET_underX'] = (GetSetting("PET_underX", 40) * Var['MembersAmount']) / 100
            Settings['PET_underX_val'] = GetSetting("PET_underX_val", 75)
        end

        -- Load general settings
        Settings['Evangelism_smart'] = GetSetting("Evangelism_smart", true)
        Settings['dps_with_atonement'] = 45
        Settings['dps_with_atonement_health'] = 95

        -- Group health thresholds
        Settings['VE_groupHP'] = GetSetting("VE_groupHP", 80)

        -- Individual unit health thresholds
        Settings['PSTanksHP'] = GetSetting("PSTanksHP", 35)
        Settings['PSMembersHP_check'] = GetSetting("PSMembersHP_check", true)
        Settings['PSMembersHP_spin'] = GetSetting("PSMembersHP_spin", 30)
        Settings['PWSHP'] = GetSetting("PWSHP", 90)
        Settings['PWSTANKHP'] = GetSetting("PWSTANKHP", 80)
        Settings['EVANHP'] = GetSetting("EVANHP", 38)
        Settings['RenewHP'] = GetSetting("RenewHP", 50)
        Settings['renewMovement'] = GetSetting("renewMovement", true)
        Settings['FlashHealHP'] = GetSetting("FlashHealHP", 50)
        Settings['FlashHealHP_Surge'] = GetSetting("FlashHealHP_Surge", 82)
        Settings['FlashHealHP_EH'] = GetSetting("FlashHealHP_EH", 35)
        Settings['PenanceHP_EH'] = GetSetting("PenanceHP_EH", 45)
        Settings['VoidShift_Self_check'] = GetSetting("VoidShift_Self_check", true)
        Settings['VoidShift_Self_spin'] = GetSetting("VoidShift_Self_spin", 35)
        Settings['VoidShift_Members_check'] = GetSetting("VoidShift_Members_check", true)
        Settings['VoidShift_Members_spin'] = GetSetting("VoidShift_Members_spin", 38)

        -- Defensive cooldowns
        Settings['DesperatePrayerHP'] = GetSetting("DesperatePrayerHP", 35)

        -- Utility settings
        Settings['angelic_check'] = GetSetting('angelic_check', true)
        Settings['angelic_spin'] = GetSetting('angelic_spin', 2.5)
        Settings['bsoul_check'] = GetSetting('bsoul_check', true)
        Settings['bsoul_spin'] = GetSetting('bsoul_spin', 2.5)
        Settings['ShadowfiendMana'] = GetSetting("ShadowfiendMana", 50)
        Settings['SA'] = GetSetting("SA", {2})
        Settings['bender_x_radiance'] = GetSetting("bender_x_radiance", true)
        Settings['bender_x_premonition'] = GetSetting("bender_x_premonition", true)
        Settings['autoPS'] = GetSetting("autoPS", false)
        Settings['magicgroundspell'] = GetSetting('magicgroundspell', 1)
        Settings['pi_fullauto'] = GetSetting("pi_fullauto", true)
        Priest.PiTargetName_Auto_Enabled = Settings['pi_fullauto']
        Priest.PiTargetName_Auto_Toast = GetSetting("pi_fullauto_toast", true)

        -- Premonition talent settings
        Settings['PremonitionOfInsight'] = GetSetting("PremonitionOfInsight", 78)
        Settings['PremonitionOfPiety'] = GetSetting("PremonitionOfPiety", 70)
        Settings['PremonitionOfSolace'] = GetSetting("PremonitionOfSolace", 68)
        Settings['PremonitionOfClairvoyance'] = GetSetting("PremonitionOfClairvoyance", 50)
        Settings['PoIusage'] = GetSetting("PoIusage", "PoIauto")
        Settings['PoSusage'] = GetSetting("PoSusage", "PoSauto")
        Settings['InsightPietyCombo'] = GetSetting("InsightPietyCombo", true)
        
        -- Ensure combat state is accurate even if Player:AffectingCombat() is false
        if not Var['IsInCombat'] then
            Var['IsInCombat'] = combatMonitor()
        end
    end

    -- Basic evaluation function that always returns true
    local function EvaluateTrue()
        return true
    end
    
    -- List of NPCs to exclude from certain actions
    local ExcludeNPCList = {
        -- Spiteful Shades
        [174773] = true
    }
    
    ------ Target Evaluation Functions ------
    
    ---@param TargetUnit Unit
    -- Returns unit's health percentage for sorting
    local function EvaluateTargetIfHP(TargetUnit)
        return TargetUnit:HealthPercentage()
    end

    ---@param TargetUnit Unit
    -- Returns unit's health without percentage for sorting
    local function EvaluateTargetIfHealthNoPercentage(TargetUnit)
        return TargetUnit:Health()
    end

    ---@param TargetUnit Unit
    -- Returns unit's max health for sorting
    local function EvaluateTargetIfMaxHP(TargetUnit)
        return TargetUnit:MaxHealth()
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if unit is a Spiteful Shade
    local function EvaluateSpiteful(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    
    ---@param TargetUnit Unit
    -- Checks if unit is a Spiteful Shade that needs to be controlled
    local function IsSpiteful(TargetUnit)
        return TargetUnit:NPCID() == 174773 and TargetUnit:TimeToDie() > 2 and Player:IsTanking(TargetUnit)
    end
    
    ------ Power Infusion Evaluation Functions ------
    
    ---@param TargetUnit Unit
    -- Basic Power Infusion evaluation
    local function EvaluatePI(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true)
        and (not TargetUnit:IsUnit(Player) or not IsInGroup())
        and TargetUnit:CurrentTarget()
    end
    
    ---@param TargetUnit Unit
    -- Power Infusion evaluation with toggle preference
    local function EvaluatePIToggle(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true)
        and TargetUnit:Name() == M.Priest.PiTargetName 
        and TargetUnit:CurrentTarget()
    end
    
    ---@param TargetUnit Unit
    local function EvaluatePIAuto(TargetUnit)
        if TargetUnit:Name() ~= Priest.PiTargetName_Auto then
            return false
        end

        if TargetUnit:BuffUp(S.PowerInfusionBuff, true) then
            Priest.PiTargetName_Auto = ""
            return false
        end

        return true
    end
    
    ------ Spell Target Evaluation Functions ------
    ---@param TargetUnit Unit
    -- Evaluates if Shadow Word: Pain should be applied
    local function EvaluatePain(TargetUnit)
        return TargetUnit:DebuffRefreshable(S.ShadowWordPainDebuff) 
        and S.PowerWordShield:CooldownRemains() > 5 
        and TargetUnit:TimeToDie() > 14
        and not ExcludeNPCList[TargetUnit:NPCID()]
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Pain Suppression should be used on a tank
    local function EvaluatePSTanks(TargetUnit)
        return TargetUnit:RealHealthPercentage() <= Settings['PSTanksHP'] 
        and TargetUnit:BuffDown(S.PainSuppression, true) 
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Pain Suppression should be used on group members
    local function EvaluatePSMembers(TargetUnit)
        return TargetUnit:RealHealthPercentage() <= Settings['PSMembersHP_spin'] 
        and TargetUnit:BuffDown(S.PainSuppression, true)
    end
    
    ------ Void Shift Evaluation Functions ------
    
    ---@param TargetUnit Unit
    -- Basic health percentage check for Void Shift
    local function EvaluateVoidShiftTargetIf(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Void Shift should be used defensively by the player
    local function EvaluateVoidShiftSelf(TargetUnit)
        return not TargetUnit:IsUnit(Player) 
            and TargetUnit:HealthPercentage() - Player:RealHealthPercentage() >= 50
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Void Shift should be used to save a group member
    local function EvaluateVoidShiftMembers(TargetUnit)
        return not TargetUnit:IsUnit(Player) 
            and TargetUnit:RealHealthPercentage() <= Settings['VoidShift_Members_spin'] 
            and Player:RealHealthPercentage() - TargetUnit:RealHealthPercentage() >= 50
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Shadow Word: Death can be used with Entropic Rift
    local function EvaluateSWDEntropic(TargetUnit)
        return (TargetUnit:HealthPercentage() < 50)
    end
    
    ------ Healing Evaluation Functions ------
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Life should be used
    local function EvaluatePowerWordLife(TargetUnit)
        return (TargetUnit:Health() / TargetUnit:MaxHealth() * 100) < 35
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be used on tanks
    local function EvaluatePWSTanks(TargetUnit)
        return TargetUnit:BuffDown(S.PowerWordShield) 
            and TargetUnit:BuffDown(S.Atonement) 
            and (TargetUnit:HealthPercentage() <= 99 or TargetUnit:AffectingCombat())
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be used based on health
    local function EvaluatePWS(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['PWSHP'] 
            and TargetUnit:BuffDown(S.PowerWordShield) 
            and TargetUnit:BuffDown(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Evangelism should be used on a unit with Atonement
    local function EvaluateEVAN(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['EVANHP'] 
            and TargetUnit:BuffUp(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be used as a filler
    local function EvaluatePWSFiller(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['PWSHP'] 
            and TargetUnit:BuffDown(S.PowerWordShield)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be used to maintain uptime
    local function EvaluatePWSUptime(TargetUnit)
        return TargetUnit:BuffDown(S.PowerWordShield) 
        and TargetUnit:BuffDown(S.Atonement) 
        and (TargetUnit:HealthPercentage() <= 99 or TargetUnit:AffectingCombat())
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Renew should be applied based on health
    local function EvaluateRenew(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['RenewHP'] 
        and TargetUnit:BuffDown(S.Renew) 
        and TargetUnit:BuffDown(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Renew should be applied during ramp
    local function EvaluateRenewRamp(TargetUnit)
        return TargetUnit:BuffDown(S.Renew)
    end

    ---@param TargetUnit Unit
    local function EvaluateRenewFiller(TargetUnit)
        return TargetUnit:BuffDown(S.Renew) and TargetUnit:HealthPercentage() <= 95
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be applied during ramp
    local function EvaluatePWSRamp(TargetUnit)
        return TargetUnit:BuffDown(S.PowerWordShield)
    end

    ---@param TargetUnit Unit
    -- Evaluates Power Word: Shield unit with lowest runtime
    local function EvaluatePWSLowestRuntime(TargetUnit)
        return TargetUnit:BuffRemains(S.PowerWordShield)
    end

    ---@param TargetUnit Unit
    -- Evaluates PW:S for tanks
    local function EvaluatePWSforTanks(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['PWSTANKHP']
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Flash Heal should be used based on health
    local function EvaluateFlashHeal(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['FlashHealHP']
    end

    ---@param TargetUnit Unit
    -- Evaluates if Flash Heal should be used based on health
    local function EvaluateFlashHealUnderInsight(TargetUnit)
        return TargetUnit:HealthPercentage() < 70
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if unit needs Atonement
    local function EvaluateAtonement(TargetUnit)
        return TargetUnit:BuffDown(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Flash Heal should be used out of combat
    local function EvaluateFlashHealOOC(TargetUnit)
        return TargetUnit:HealthPercentage() <= 95
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Prayer of Mending should be applied
    local function EvaluatePoM(TargetUnit)
        return TargetUnit:BuffDown(S.PrayerofMendingBuff) 
            and TargetUnit:AffectingCombat() 
            and TargetUnit:HealthPercentage() <= 99
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Radiance should be used
    local function EvaluateRadiance(TargetUnit)
        return TargetUnit:BuffRefreshable(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Renew should be applied on tanks
    local function EvaluateRenewTanks(TargetUnit)
        return TargetUnit:BuffDown(S.Atonement)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Flash Heal should be used with Surge of Light
    local function EvaluateFlashHealSurge(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['FlashHealHP_Surge']
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Flash Heal should be used for emergency healing
    local function EvaluateFlashHealEmergency(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['FlashHealHP_EH']
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Penance should be used for emergency healing
    local function EvaluatePenanceEmergency(TargetUnit)
        -- When Inescapable Torment is talented and the pet is active, we want to cast Penance on cooldown rather than saving it
        if Var and Var['AggressivePenance'] then
            return false
        end
        return TargetUnit:HealthPercentage() <= Settings['PenanceHP_EH']
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Shield should be used for Body and Soul
    local function EvaluatePWSBS(TargetUnit)
        return TargetUnit:IsUnit(Player)
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Penance should be used on targets with SW:P
    local function EvaluateSwPPenance(TargetUnit)
        return TargetUnit:DebuffUp(S.ShadowWordPainDebuff) 
            and TargetUnit:TimeToDie() > 3
            and not ExcludeNPCList[TargetUnit:NPCID()]
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if Power Word: Radiance should be used for emergency healing
    local function PowerWordRadiance(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['PWR_EH_underX_val']
    end
    
    ---@param TargetUnit Unit
    -- Evaluates if unit needs Power Word: Shield with Premonition of Insight
    local function EvaluatePremonitionOfInsight(TargetUnit)
        return TargetUnit:DebuffDown(S.PowerWordShield)
    end

    ---@param TargetUnit Unit
    local function EvaluatePWS_DMGINC(TargetUnit)
        return TargetUnit:BuffDown(S.Atonement) and TargetUnit:BuffDown(S.PowerWordShield)
    end

    -- Provides basic utility functions like buffing and movement abilities
    local function Utilities()
        -- Group buff: Power Word: Fortitude
        if S.PowerWordFortitude:IsReady(Player) then 
           if Player:BuffDown(S.PowerWordFortitude, true) or M.GroupBuffMissing(S.PowerWordFortitude) then
                if Cast(S.PowerWordFortitude) then
                    return "Power Word: Fortitude"
                end
            end
        end
    
        -- Combat utilities
        if Var['IsInCombat'] then
            -- Threat management with Fade when tanking mobs
            if not Var['Fade'] and not Var['Shadowmeld'] and Player:IsInPartyOrRaid() and Player:IsTankingAoE(40) then
                if S.Fade:IsReady(Player) then
                    if Cast(S.Fade) then
                        return "Fade (Threat Drop)"
                    end
                end
            end
        else
            -- Resurrection for dead party members when out of combat
            if Target:IsDeadOrGhost() and Target:IsInParty() and Target:IsAPlayer() and not Target:IsEnemy() then
                if S.Resurrection:IsReady(Player) then
                    if Cast(S.Resurrection) then
                        return "Resurrection"
                    end
                end
            end
        end
    
        -- Movement speed management
        if not Var['BodyAndSoulBuff'] and not Var['AngelicFeatherBuff'] then
            -- Body and Soul - Shield for movement speed
            if Settings['bsoul_check'] and S.BodyAndSoul:IsAvailable() and S.PowerWordShield:IsReady(Player) and
               Player:IsMovingFor() > Settings['bsoul_spin'] then
                if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWSBS) then
                    return "Power Word: Shield (Body and Soul)"
                end
            -- Angelic Feather - Targeted movement speed boost
            elseif Settings['angelic_check'] and S.AngelicFeather:IsReady(Player) and
                  Player:IsMovingFor() > Settings['angelic_spin'] then
                if Cast(S.AngelicFeather) then
                    return "Angelic Feather"
                end
            end
        end
    end

    local function Trinkets()
        -- if I.IridaltheEarthsMaster:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
        --     if Cast(I.IridaltheEarthsMaster) then return "iridal_the_earths_master"; end
        -- end

        -- if I.Dreambinder:IsEquippedAndReady() then
        --     if MainAddon.SetTopTexture(1, "Weapon On-Use") then return "dreambinder_loom_of_the_great_cycle"; end
        -- end
 	end

    -- Manages personal defensive cooldown usage
    local function Defensives()
        -- Desperate Prayer - Major personal health boost
        if Player:RealHealthPercentage() <= Settings['DesperatePrayerHP'] and S.DesperatePrayer:IsReady(Player) then
            if Cast(S.DesperatePrayer, true) then
                return "Desperate Prayer"
            end
        end
        
        -- React to incoming heavy damage (tank busters)
        local Should = Player:ShouldUseDefensive();
        if Should and S.PowerWordShield:IsReady(Player) then
            if CastAlly(S.PowerWordShield, Player) then
                return "Power Word: Shield (Tank Buster)"
            end
        end
    end

    -- Handles Focus-target specialized healing when needed
    local function FocusHealing()
        -- Only heal friendly targets in range
        if not Focus:IsInRange(40) or Player:CanAttack(Focus) then
            return false
        end
        
        -- Emergency healing: Power Word: Life for critically low targets (<35%)
        if S.PowerWordLife:IsReady(Focus, nil, nil, nil, nil, nil, true) and 
           (Focus:Health() / Focus:MaxHealth() * 100) < 35 then
            if CastAlly(S.PowerWordLife, Focus) then
                return "Power Word: Life (Emergency)"
            end
        end
        
        -- Apply Atonement if not present
        if Focus:BuffDown(S.Atonement) then
            -- Prefer Shield for Atonement application
            if S.PowerWordShield:IsReady(Focus) then
                if CastAlly(S.PowerWordShield, Focus) then
                    return "Atonement via Shield"
                end
            -- Fall back to Renew if Shield unavailable
            elseif S.Renew:IsReady(Focus) and Focus:BuffDown(S.Renew) then
                if CastAlly(S.Renew, Focus) then
                    return "Atonement via Renew"
                end
            end
        end
        
        -- Use Penance for burst healing
        if Penance:IsReady(Focus) then
            if CastAlly(Penance, Focus) then
                return "Penance"
            end
        end          
        
        -- Ensure Atonement is maintained
        if Focus:BuffDown(S.Atonement) and S.PowerWordShield:IsReady(Focus) then
            if CastAlly(S.PowerWordShield, Focus) then
                return "Refresh Atonement"
            end
        else
            -- Direct healing when Atonement is already active
            if S.FlashHeal:IsReady(Focus) then
                if CastAlly(S.FlashHeal, Focus) then
                    return "Flash Heal"
                end
            end
        end
        
        return false
    end

    -- Manages the Ramp rotation when activated by the Ramp toggle
    local function RampToggle()
        -- Check if we're playing Voidweaver or Oracle spec
        if S.EntropicRift:IsAvailable() then
            -- ------------------------------------
            -- Voidweaver Ramp Sequence
            -- ------------------------------------
            
            -- Step 1: Apply Shadow Word: Pain on target
            if S.ShadowWordPain:IsReady() and Target:DebuffRefreshable(S.ShadowWordPain) then
                if Cast(S.ShadowWordPain) then
                    return "Shadow Word: Pain"
                end
            end
            
            -- Step 2: Apply Power Word: Shield to spread Atonement
            if S.PowerWordShield:IsReady(Player) and Var['BuffAtonementInGroup'] < Var['MembersAmount'] then
                if CastTargetIfAlly(S.PowerWordShield, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateAtonement) then
                    return "Power Word: Shield"
                end
            end
            
            -- Step 3: Use Power Word: Radiance to spread more Atonement
            if S.PowerWordRadiance:IsReady(Player) and Var['BuffAtonementInGroup'] < Var['MembersAmount'] then
                if CastCycleAlly(S.PowerWordRadiance, Members, EvaluateRadiance) then
                    return "Power Word: Radiance"
                end
            end
            
            -- Step 4: Use Mindbender/Shadowfiend after spreading Atonement
            if (Var['BuffAtonementInGroup'] >= Var['MembersAmount'] * 0.8 or 
                Player:PrevGCDP(1, S.PowerWordRadiance) or 
                S.PowerWordRadiance:TimeSinceLastCast() <= 2) and
                Var['Fiend']:IsReady() then
                if Cast(Var['Fiend']) then
                    return "Mindbender/Shadowfiend"
                end
            end
            
            -- Step 5: Use offensive abilities once Atonement is spread
            if (Var['BuffAtonementInGroup'] >= Var['MembersAmount'] * 0.8 or Var['Fiend']:CooldownRemains() > 0) then
                -- Mind Blast has highest priority
                if S.MindBlast:IsReady() then
                    if Cast(S.MindBlast) then
                        return "Mind Blast"
                    end
                end
                
                -- Penance is second priority
                if Penance:IsReady() then
                    if Target:DebuffUp(S.ShadowWordPainDebuff) then
                        if Cast(Penance, "Penance Enemy") then
                            return "Penance (Target)"
                        end
                    else
                        if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                            return "Penance (MO)"
                        end
                    end
                end
                
                -- Void Blast is third priority
                if S.VoidBlast:IsReady() then
                    if Cast(S.VoidBlast) then
                        return "Ramp: Void Blast"
                    end
                end
            end
            
            -- Fall back to spreading Atonement with Renew if Radiance charges are depleted
            if S.PowerWordRadiance:Charges() == 0 and Var['BuffAtonementInGroup'] < Var['MembersAmount'] then
                if S.Renew:IsReady(Player) then
                    if CastCycleAlly(S.Renew, Members, EvaluateAtonement) then
                        return "Ramp: Spread Atonement with Renew"
                    end
                end
            end
            
            -- Ramp Completion Check - turn off when we've done our burst sequence
            if Var['BuffAtonementInGroup'] >= Var['MembersAmount'] * 0.8 and 
            Var['Fiend']:CooldownRemains() > 0 and
            S.MindBlast:CooldownRemains() > 0 and
            Var['PenanceCooldownRemains'] > 0 then
                MainAddon.Toggle:SetToggle("Ramp", false)
                return "Ramp Complete"
            end
        else
            -- Oracle Ramp
            -- Step 1: Apply SW:P on target if needed
            if S.ShadowWordPain:IsReady() and Target:DebuffRefreshable(S.ShadowWordPain) then
                if Cast(S.ShadowWordPain) then
                    return "Ramp Oracle: Shadow Word: Pain"
                end
            end
            
            -- Step 2: Apply PW:S on lowest party member
            if S.PowerWordShield:IsReady(Player) then
                if CastTargetIfAlly(S.PowerWordShield, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Ramp Oracle: Power Word: Shield (Lowest)"
                end
            end
            
            -- Step 3: Apply Renew on party member without Renew
            if S.Renew:IsReady(Player) then
                if CastCycleAlly(S.Renew, Members, EvaluateRenewRamp) then
                    return "Ramp Oracle: Renew"
                end
            end
            
            -- Step 4: Use Power Word: Radiance
            if S.PowerWordRadiance:IsReady(Player) and S.PowerWordRadiance:ChargesFractional() > 1.55 then
                if CastCycleAlly(S.PowerWordRadiance, Members, EvaluateTrue) then
                    return "Ramp Oracle: Power Word: Radiance"
                end
            end
            
            -- Step 5: Use Mindbender/Shadowfiend
            if Var['Fiend']:IsReady() then
                if Cast(Var['Fiend']) then
                    return "Ramp Oracle: Mindbender/Shadowfiend"
                end
            end
            
            -- Step 6: Apply PW:S on another party member
            if S.PowerWordShield:IsReady(Player) then
                if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWSRamp) then
                    return "Ramp Oracle: Power Word: Shield (Another)"
                end
            end
            
            -- Ramp Completion Check
            if Var['Fiend']:CooldownRemains() > 0 and S.PowerWordShield:CooldownRemains() > 0 then
                MainAddon.Toggle:SetToggle("Ramp", false)
                return "Oracle Ramp Complete"
            end
        end
    end
    
    local function HealingSpecialMO()
        -- Check if mouseover exists and is in range
        if not MouseOver:IsInRange(40) or Player:CanAttack(MouseOver) then
            return false
        end
        
        -- If mouseover and focus are the same unit, proceed with healing
        if Focus:GUID() == MouseOver:GUID() then
            local healingResult = FocusHealing()
            if healingResult then
                return healingResult
            end
        else
            -- Visual indicator that mouseover is not the current focus
            MainAddon.SetTopColor(1, "Focus Mouseover")
        end
        
        return false
    end
    
    -- Handles special healing requests for target/mouseover through HealingEngine
    local function HealingSpecial()
        local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
        
        if shouldHeal and isReadyToBeHealed then
            local healingResult = FocusHealing()
            if healingResult then
                return healingResult
            end
        elseif not isReadyToBeHealed then
            -- Visual indicator for UI when target is not ready for healing
            if type == "MouseOver" then
                MainAddon.SetTopColor(1, "Focus Mouseover")
            elseif type == "Target" then
                MainAddon.SetTopColor(1, "Focus Target")
            end
        end
        
        return false
    end

    -- Handles predicted incoming damage
    local function DamageIncoming()
        -- Step 1: Apply Power Word: Shield to players without Atonement
        if S.PowerWordShield:IsReady(Player) and not S.PowerWordRadiance:IsReady(Player) and Var['BuffAtonementInGroup'] < 5 then
            if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWS_DMGINC) then
                return "Shield for Atonement Application"
            end
        end
        
        -- Step 2: Spread Atonement with Power Word: Radiance
        if S.PowerWordRadiance:IsReady(Player) and S.PowerWordRadiance:ChargesFractional() >= 1 then
            if CastCycleAlly(S.PowerWordRadiance, Members, EvaluateRadiance) then
                return "Power Word: Radiance"
            end
        end
        
        -- Step 3: Follow Radiance with Mindbender/Shadowfiend for damage boost
        if Player:PrevGCDP(1, S.PowerWordRadiance) or S.PowerWordRadiance:TimeSinceLastCast() <= 2 then
            if Var['Fiend']:IsReady() then
                if Cast(Var['Fiend']) then
                    return "Damage Incoming: Mindbender/Shadowfiend"
                end
            end
        end
        
        -- Step 4: Deal damage for Atonement healing once at least 3 Atonements are active
        if Var['BuffAtonementInGroup'] > 2 then
            -- Mind Blast (highest priority damage ability)
            if S.MindBlast:IsReady() then
                if Cast(S.MindBlast) then
                    return "Damage Incoming: Mind Blast"
                end
            end

            -- Penance on Target or enemies with Shadow Word: Pain
            if Penance:IsReady() then
                if Target:DebuffUp(S.ShadowWordPainDebuff) then
                    if Cast(Penance, "Penance Enemy") then
                        return "Damage Incoming: Penance (Target)"
                    end
                else
                    if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                        return "Damage Incoming: Penance (MO)"
                    end
                end
            end
            
            -- Shadow Word: Death
            if S.ShadowWordDeath:IsReady() then
                if Cast(S.ShadowWordDeath) then
                    return "Damage Incoming: Shadow Word: Death"
                end
            end
        end
    end

    -- Manages Power Infusion targeting and usage
    local function PowerInfusion()
        -- Only proceed if Power Infusion is ready and fight will last long enough
        if not S.PowerInfusion:IsReady(Player) or FightRemains <= 15 then
            return false
        end
        
        if Settings['pi_fullauto'] then
            local piName = Priest.PiTargetName_Auto or ""
            if piName ~= "" then
                local now = GetTime()
                if now - Priest.PILastSuggestionTime < 1.5 then
                    return false
                end

                if CastCycleAlly(S.PowerInfusion, Priest.MembersPIAuto, EvaluatePIAuto) then
                    if Priest.PiTargetName_Auto_Toast then
                        MainAddon.UI:ShowToast("Power Infusion", "Cast on " .. piName, MainAddon.GetTexture(S.PowerInfusion))
                    end
                    return "Cooldowns: Auto Power Infusion on " .. piName
                end
            end
        else
            if Priest.PiTargetName == "None" then
                if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePI) then
                    return "Cooldowns: Power Infusion (Auto Target)"
                end
            else
                if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePIToggle) then
                    return "Cooldowns: Power Infusion on " .. Priest.PiTargetName
                end
            end
        end
    end

    -- Manages major healing cooldowns and utility abilities
    local function HealingCDs()

        -- ----------------------------------------------------------------
        -- Power Word: Barrier / Luminous Barrier
        -- ----------------------------------------------------------------
        if HealingEngine:MembersUnderPercentage(Settings['PWBar_underX_val']) >= Settings['PWBar_underX'] then
            if S.LuminousBarrier:IsReady() then
                if Cast(S.LuminousBarrier) then
                    return "Luminous Barrier"
                end
            end

            if S.PowerWordBarrier:IsReady() then
                local magicgroundspell_target = Settings['magicgroundspell']

                if magicgroundspell_target == 1 then
                    if #Members > 0 and Var['LowestUnit'] then
                        if CastMagicAlly(S.PowerWordBarrier, Var['LowestUnit'], nil, "62618-Magic") then 
                            return "Damage Incoming: Power Word: Barrier - Friend"
                        end
                    end
                end

                if magicgroundspell_target == 2 then
                    if Player:CanAttack(Target) then
                        if CastMagic(S.PowerWordBarrier, nil, "62618-Magic") then 
                            return "Damage Incoming: Power Word: Barrier - Enemy"
                        end
                    end
                end

                if magicgroundspell_target == 3 then
                    if Cast(S.PowerWordBarrier) then
                        return "Damage Incoming: Power Word: Barrier - Regular"
                    end
                end
            end
        end

        -- ----------------------------------------------------------------
        -- Premonition Abilities (Oracle)
        -- ----------------------------------------------------------------
        if not S.EntropicRift:IsLearned() then
            -- Premonition of Insight - Empowers Penance
            if S.PremonitionOfInsight:IsReady(Player) and Penance:IsReady() and 
               Var['AverageHPInRange'] <= Settings['PremonitionOfInsight'] then
                -- Use Mindbender before Premonition if setting enabled
                if Settings['bender_x_premonition'] and Var['Fiend']:IsReady() then
                    if Cast(Var['Fiend']) then
                        return "Fiend before Premonition of Insight"
                    end
                end
                
                if Cast(S.PremonitionOfInsight) then
                    return "Premonition of Insight"
                end
            end
            
            -- Premonition of Piety - AoE healing via Atonement
            if S.PremonitionOfPiety:IsReady(Player) and Var['AverageHPInRange'] <= Settings['PremonitionOfInsight'] then
                -- Use Mindbender before Premonition if setting enabled
                if Settings['bender_x_premonition'] and Var['Fiend']:IsReady() then
                    if Cast(Var['Fiend']) then
                        return "Fiend before Premonition of Piety"
                    end
                end
                
                if Cast(S.PremonitionOfPiety) then
                    return "Premonition of Piety"
                end
            end
            
            -- Premonition of Solace - Single target emergency healing
            if S.PremonitionOfSolace:IsReady(Player) and Penance:IsReady() and 
               HealingEngine:MembersUnderPercentage(Settings['PremonitionOfSolace']) >= 1 then
                if Cast(S.PremonitionOfSolace) then
                    return "Premonition of Solace"
                end
            end
            
            -- Premonition of Clairvoyance - Absorption shield
            if Settings['PremonitionOfClairvoyance'] and 
               S.PremonitionOfClairvoyance:IsReady(Player) and Penance:IsReady() and 
               Var['AverageHPInRange'] <= Settings['PremonitionOfClairvoyance'] then
                if Cast(S.PremonitionOfClairvoyance) then
                    return "Premonition of Clairvoyance"
                end
            end
        end
        
        -- ----------------------------------------------------------------
        -- Pet
        -- ----------------------------------------------------------------
        if Var['Fiend']:IsReady()  then
            -- Send Pet if members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['PET_underX_val']) >= Settings['PET_underX'] then
                if Cast(Var['Fiend']) then
                    return "Send Pet"
                end
            end
        end

        -- ----------------------------------------------------------------
        -- Mana Restoration
        -- ----------------------------------------------------------------
        if Var['TargetIsValid'] and Var['ManaPct'] <= Settings['ShadowfiendMana'] then
            -- Only cast if not syncing with Radiance or Premonition
            if not Settings['bender_x_radiance'] and not Settings['bender_x_premonition'] then
                -- Cast Mindbender if available
                if Var['Fiend']:IsReady() then
                    if Cast(Var['Fiend']) then
                        return "Mindbender (Mana Restoration)"
                    end
                end
                
                -- Fallback to Shadowfiend if Mindbender not selected
                if S.Shadowfiend:IsReady() then
                    if Cast(S.Shadowfiend) then
                        return "Shadowfiend (Mana Restoration)"
                    end
                end
            end
        end

        -- ----------------------------------------------------------------
        -- Emergency Healing
        -- ----------------------------------------------------------------
        -- Power Word: Life - Emergency heal for critically low targets
        if S.PowerWordLife:IsReady(Player, nil, nil, nil, nil, nil, true) then
            if CastCycleAlly(S.PowerWordLife, Members, EvaluatePowerWordLife) then
                return "Power Word: Life (Emergency)"
            end
        end
        
        -- Pre-shield in 5-mans when we have partial Atonement coverage
        if not Player:IsInRaidArea() then
            if #Var['MembersWithAtonement'] >= 3 and #Var['MembersWithAtonement'] < 5 then
                if S.PowerWordShield:IsReady(Player) then
                    if CastCycleAlly(S.PowerWordShield, Var['MembersWithoutAtonement'], function(TargetUnit)
                        return TargetUnit:BuffDown(S.PowerWordShield) and TargetUnit:BuffDown(S.Atonement) and TargetUnit:HealthPercentage() <= 99
                    end) then
                        return "Cooldowns: Pre-emptive Shield"
                    end
                end
            end
        end

        -- Ultimate Penitence - AoE healing cooldown
        if S.UltimatePenitence:IsReady(Player) then
            if Var['AtonementPercentage'] >= Settings['UP_Atonement_val'] then
                if Var['AtonementAverage'] >= 4.5 then
                    if HealingEngine:MembersUnderPercentage(Settings['UP_underX_val']) >= Settings['UP_underX'] then
                        if Var['MembersWithAtonementHealthAverage'] <= Settings['dps_with_atonement_health'] then
                            if Cast(S.UltimatePenitence) then
                                return "Cooldowns: Ultimate Penitence"
                            end
                        end
                    end
                end
            end
        end

        -- Power Word: Radiance - Group Atonement application
        if S.PowerWordRadiance:IsReady(Player) then
            -- Standard application based on health thresholds
            if HealingEngine:MembersUnderPercentage(Settings['PWR_underX_val']) >= Settings['PWR_underX'] then
                if CastTargetIfAlly(S.PowerWordRadiance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateRadiance) then
                    return "Cooldowns: Power Word: Radiance"
                end
            end

            -- Refresh Atonement when duration is low and health is a concern
            if Var['AtonementAverage'] <= 4.0 then
                if Var['MembersWithAtonementHealthAverage'] <= Settings['dps_with_atonement_health'] then
                    if HealingEngine:MembersUnderPercentage(Settings['PWR_underX_val']) >= Settings['PWR_underX'] then
                        if CastTargetIfAlly(S.PowerWordRadiance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                            return "Cooldowns: Power Word: Radiance (Refresh)"
                        end
                    end
                end
            end
        end      

        -- Evangelism - Extends Atonement duration
        if S.Evangelism:IsReady(Player) then                  
            -- AoE situation - Multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['EVAN_underX_val']) >= Settings['EVAN_underX'] and
            Var['AtonementPercentage'] >= Settings['EVAN_Atonement_val'] then
                if Cast(S.Evangelism) then
                    return "Cooldowns: Evangelism (Group-wide)"
                end
            end
        end
        
        -- Single Target Evangelism for critical targets
        if S.Evangelism:IsReady(Player) then
            local shouldCast = false
            
            -- Check if any member meets the critical criteria
            for i = 1, #Members do
                if EvaluateEVAN(Members[i]) then
                    shouldCast = true
                    break
                end
            end
            
            if shouldCast then
                if Cast(S.Evangelism) then
                    return "Cooldowns: Evangelism (Critical Target)"
                end
            end
        end

        -- Pain Suppression - Major damage reduction
        if S.PainSuppression:IsReady(Player) then 
            -- Prioritize tanks
            if CastCycleAlly(S.PainSuppression, Tanks, EvaluatePSTanks, nil, nil, nil, 0.69) then
                return "Cooldowns: Pain Suppression on Tank"
            end

            -- Support other group members if enabled
            if Settings['PSMembersHP_check'] then
                if CastCycleAlly(S.PainSuppression, Members, EvaluatePSMembers) then
                    return "Cooldowns: Pain Suppression on Group Member"
                end
            end
        end

        -- Vampiric Embrace - Shadow healing cooldown
        if S.VampiricEmbrace:IsReady(Player) and Player:BuffUp(S.ShadowCovenantBuff) and Var['TargetIsValid'] then
            if Var['AverageHPInRange'] <= Settings['VE_groupHP'] then
                -- Use racial abilities to boost healing
                -- Berserking (Troll)
                if S.Berserking:IsReady(Player) then
                    if Cast(S.Berserking, true) then
                        return "Cooldowns: Berserking with Vampiric Embrace"
                    end
                end
                -- Blood Fury (Orc)
                if S.BloodFury:IsReady(Player) then
                    if Cast(S.BloodFury, true) then
                        return "Cooldowns: Blood Fury with Vampiric Embrace"
                    end
                end
                -- Ancestral Call (Mag'har Orc)
                if S.AncestralCall:IsReady(Player) then
                    if Cast(S.AncestralCall, true) then
                        return "Cooldowns: Ancestral Call with Vampiric Embrace"
                    end
                end
                -- Cast Vampiric Embrace
                if Cast(S.VampiricEmbrace, true) then
                    return "Cooldowns: Vampiric Embrace"
                end
            end
        end

        -- Void Shift - Health exchange ability
        if S.VoidShift:IsReady(Player) then
            -- Use Void Shift as a personal defensive
            if Settings['VoidShift_Self_check'] and Player:RealHealthPercentage() <= Settings['VoidShift_Self_spin'] then
                if CastTargetIfAlly(S.VoidShift, TargetIfAlly, "max", EvaluateVoidShiftTargetIf, EvaluateVoidShiftSelf, true) then
                    return "Cooldowns: Void Shift (Self-Preservation)"
                end
            end

            -- Use Void Shift to save critically low group members
            if Settings['VoidShift_Members_check'] then
                if CastTargetIfAlly(S.VoidShift, TargetIfAlly, "min", EvaluateVoidShiftTargetIf, EvaluateVoidShiftMembers, true) then
                    return "Cooldowns: Void Shift (Group Member Rescue)"
                end
            end
        end
    end

    local function HealingRotation()
        -- Inescapable Torment Handling: aggressively cast Penance on cooldown when the talent is taken and the pet is active
        if Var['AggressivePenance'] and Penance:IsReady() then
            if Var['TargetIsValid'] then
                if Target:DebuffUp(S.ShadowWordPainDebuff) then
                    if Cast(Penance, "Penance Enemy") then
                        return "Inescapable Torment: Penance (Target)";
                    end
                else
                    if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                        return "Inescapable Torment: Penance (MO)";
                    end
                end
            end
        end
        -- Empowered Shield > Everything else
        if not S.EntropicRift:IsAvailable() and Player:BuffUp(S.WealandWoeBuff) then
            if S.PowerWordShield:IsReady(Player) then
                -- First priority: Tanks with less than 80% HP and no Shield
                if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluatePWSforTanks) then
                    return "Oracle: Cast Power Word: Shield on Low HP Tank (<80%)"
                end
    
                -- Second priority: Members without shield, with priority to lowest HP
                if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfHealthNoPercentage, EvaluatePWSRamp) then
                    return "Oracle: Cast Power Word: Shield on Lowest HP Member"
                end
                
                -- Third priority: Party members without shield (using EvaluateTargetIfMaxHP)
                if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfMaxHP, EvaluatePWSRamp) then
                    return "Oracle: Cast Power Word: Shield on Member"
                end
                
                -- Fourth priority: Target party member with least remaining shield duration
                if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                    return "Oracle: Refresh Power Word: Shield on Member (lowest duration)"
                end
                
                -- Fifth priority: Tank with least remaining shield duration
                if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                    return "Oracle: Refresh Power Word: Shield on Tank (lowest duration)"
                end
            end
        end

        -- Cast Mind Blast to enter Entropic Rift
        if S.MindBlast:IsReady() and S.EntropicRift:IsAvailable() and HealingEngine:MembersUnderPercentage(Settings['ER_underX_val']) >= Settings['ER_underX'] then
            if Cast(S.MindBlast) then
                return "MB to enter Entropic Rift"
            end
        end

        -- // Emergency Healing Start \\ --
        -- Emergency Radiance: Cast Power Word: Radiance if fewer than 40% of the group have Atonement
        -- or if Atonement buffs are expiring soon, and a sufficient number of members are below the health threshold.
        if S.PowerWordRadiance:IsReady(Player) then
            if (Var['AtonementPercentage'] < 40 or Var['AtonementAverage'] < 3.0)
                and HealingEngine:MembersUnderPercentage(Settings['PWR_EH_underX_val']) >= Settings['PWR_EH_underX'] then
                if CastCycleAlly(S.PowerWordRadiance, Members, PowerWordRadiance) then
                    return "Emergency Radiance Cast"
                end
            end
        end
        -- Emergency Penance: Cast Penance if it is ready and there are two or fewer members below the emergency threshold.
        if Penance:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['PenanceHP_EH']) <= 2 then
            if CastCycleAlly(Penance, Members, EvaluatePenanceEmergency) then
                return "Emergency Penance Cast"
            end
        end
        -- Emergency Flash Heal: Cast Flash Heal if only two or fewer members are low,
        -- and the Surge of Light buff is not active.
        if S.FlashHeal:IsReady(Player) and not Player:BuffUp(S.SurgeofLightBuff)
            and (HealingEngine:MembersUnderPercentage(Settings['FlashHealHP_EH'] + 10) <= 2) then
            if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHealEmergency) then
                return "Emergency Flash Heal Cast"
            end
        end
        -- // Emergency Healing End \\ --
    
        -- Flash Heal with Surge of Light Proc: Use when the Surge of Light buff is active.
        if Player:BuffUp(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHealSurge) then
                    return "Flash Heal with Surge Proc"
                end
            end
        end
    
        -- Preserve Surge Proc: Cast Flash Heal if the Surge buff is nearly expiring.
        if Player:BuffUp(S.SurgeofLightBuff)
            and Player:BuffRemains(S.SurgeofLightBuff) <= (Var['PlayerGCD'] * HL.Latency()) * Player:BuffStack(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateAtonement) then
                    return "Flash Heal to Preserve Surge Buff"
                end
            end
        end
    
        -- High-Priority Atonement Spread via Renew: If the SpreadAtonement toggle is active and no one is below 80% health.
        if MainAddon.Toggle:GetToggle("SpreadAtonement") then
            if HealingEngine:MembersUnderPercentage(80) == 0 then
                if Settings['SA'][2] and S.Renew:IsReady(Player) then
                    if CastCycleAlly(S.Renew, Members, EvaluateAtonement) then
                        return "High Priority Atonement Spread via Renew"
                    end
                end
            end
        end
    
        -- Group Damage Abilities: Use Divine Star and Halo if the average health in range is at or below 90%.
        if Var['AverageHPInRange'] <= 90 then
            if DivineStar:IsReady(Player) then
                if Cast(DivineStar) then
                    return "Cast Divine Star"
                end
            end
            if Halo:IsReady(Player) then
                if Cast(Halo) then
                    return "Cast Halo"
                end
            end
        end
    
        -- Power Word: Shield Logic:
        -- Prioritize casting on members; if not, then on tanks. Also include a toggle for spreading Atonement.
        if S.PowerWordShield:IsReady(Player) then
            if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWS) then
                return "Cast Power Word: Shield on Member"
            end
            if CastCycleAlly(S.PowerWordShield, Tanks, EvaluatePWSTanks) then
                return "Cast Power Word: Shield on Tank"
            end
            if MainAddon.Toggle:GetToggle("SpreadAtonement") and Settings['SA'][1] then
                if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWSRamp) then
                    return "Spread Atonement: Cast Power Word: Shield"
                end
            end
        end
    
        -- Renew on Tank: Cast Renew if Power Word: Shield is on cooldown and the tank lacks Atonement.
        if S.Renew:IsReady(Player) and S.PowerWordShield:CooldownRemains() > Var['PlayerGCD'] and S.PowerWordShield:TimeSinceLastCast() > 5 then
            if CastCycleAlly(S.Renew, Tanks, EvaluateRenewTanks) then
                return "Cast Renew on Tank"
            end
        end
    
        -- Spread Atonement Renew: Use Renew to apply Atonement on members when the toggle is active.
        if MainAddon.Toggle:GetToggle("SpreadAtonement") then
            if Settings['SA'][2] and S.Renew:IsReady(Player) then
                if CastCycleAlly(S.Renew, Members, EvaluateAtonement) then
                    return "Spread Atonement via Renew (Low Priority)"
                end
            end
        end

        if not Var['IsInCombat'] then
            if S.Renew:IsReady(Player) then
                if CastCycleAlly(S.Renew, Members, EvaluateRenewFiller) then
                    return "Renew Filler"
                end
            end
        end

        -- Inescapable Torment Handling: aggressively cast Penance on cooldown when the talent is taken and the pet is active
        if Var['AggressivePenance'] and Penance:IsReady() then
            if Var['TargetIsValid'] then
                if Target:DebuffUp(S.ShadowWordPainDebuff) then
                    if Cast(Penance, "Penance Enemy") then
                        return "Inescapable Torment: Penance (Target)";
                    end
                else
                    if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                        return "Inescapable Torment: Penance (MO)";
                    end
                end
            end
        end
    end    
    
    -- Handles actions while Premonition of Insight buff is active
    local function PremonitionOfInsightBuffUp()
        -- Combo with Premonition of Piety in dangerous situations
        if Settings['InsightPietyCombo'] and Var['AverageHPInRange'] <= 50 and S.PremonitionOfPiety:IsReady(Player) then
            if Cast(S.PremonitionOfPiety) then
                return "Wombo Combo"
            end
        end
        
        -- Different usage strategies based on setting
        if Settings['PoIusage'] == "PoIPenance" then
            -- "Penance spam" mode - Use Penance on friendly units
            if Penance:IsReady(Player) then
                if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Cast Penance on Ally"
                end
            end
        elseif Settings['PoIusage'] == "PoIWW" then
            -- "Weal and Woe" mode - Use Penance to get buff, then PW:Shield to spend it
            if not Player:BuffUp(S.WealandWoeBuff) and not Player:PrevGCDP(1, Penance) then
                if Penance:IsReady(Player) then
                    if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Cast Penance on Ally for Weal and Woe"
                    end
                end
            else -- We have the Weal and Woe buff, use PW:Shield to spend it
                if S.PowerWordShield:IsReady(Player) and not Player:PrevGCDP(1, S.PowerWordShield) then
                    -- First priority: Tanks with less than 80% HP and no Shield
                    if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluatePWSforTanks) then
                        return "WnW: Cast Power Word: Shield on Low HP Tank (<80%)"
                    end
        
                    -- Second priority: Members without shield, with priority to lowest HP
                    if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfHealthNoPercentage, EvaluatePWSRamp) then
                        return "WnW: Cast Power Word: Shield on Lowest HP Member"
                    end
                    
                    -- Third priority: Party members without shield (using EvaluateTargetIfMaxHP)
                    if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfMaxHP, EvaluatePWSRamp) then
                        return "WnW: Cast Power Word: Shield on Member"
                    end
                    
                    -- Fourth priority: Target party member with least remaining shield duration
                    if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                        return "WnW: Refresh Power Word: Shield on Member (lowest duration)"
                    end
                    
                    -- Fifth priority: Tank with least remaining shield duration
                    if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                        return "WnW: Refresh Power Word: Shield on Tank (lowest duration)"
                    end
                end
            end
        elseif Settings['PoIusage'] == "PoIauto" then
            -- "Full Auto" mode - Conditional logic based on group health
            local belowThresholdCount = 0
            local groupAverageHP = Var['AverageHPInRange']
            
            -- Count members with HP% 40% lower than group average
            for i = 1, #Members do
                if Members[i]:HealthPercentage() <= (groupAverageHP - 50) then
                    belowThresholdCount = belowThresholdCount + 1
                end
            end
            
            -- If >40% of group has HP% significantly below average, use Weal and Woe logic
            if (belowThresholdCount / #Members) > 0.4 then
                -- Use Weal and Woe logic
                if not Player:BuffUp(S.WealandWoeBuff) and not Player:PrevGCDP(1, Penance) then
                    if Penance:IsReady(Player) then
                        if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                            return "Cast Penance on Ally (Auto-WW)"
                        end
                    end
                else -- We have the Weal and Woe buff, use PW:Shield to spend it
                    if S.PowerWordShield:IsReady(Player) and not Player:PrevGCDP(1, S.PowerWordShield) then
                        -- First priority: Tanks with less than 80% HP and no Shield
                        if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluatePWSforTanks) then
                            return "WnW Auto: Cast Power Word: Shield on Lowest HP Tank"
                        end
            
                        -- Second priority: Members without shield, with priority to lowest HP
                        if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfHealthNoPercentage, EvaluatePWSRamp) then
                            return "WnW Auto: Cast Power Word: Shield on Lowest HP Member"
                        end
                        
                        -- Third priority: Party members without shield (using EvaluateTargetIfMaxHP)
                        if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluateTargetIfMaxHP, EvaluatePWSRamp) then
                            return "WnW Auto: Cast Power Word: Shield on Member"
                        end
                        
                        -- Fourth priority: Target party member with least remaining shield duration
                        if CastTargetIfAlly(S.PowerWordShield, MembersWithoutTanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                            return "WnW Auto: Refresh Power Word: Shield on Member (lowest duration)"
                        end
                        
                        -- Fifth priority: Tank with least remaining shield duration
                        if CastTargetIfAlly(S.PowerWordShield, Tanks, "min", EvaluatePWSLowestRuntime, EvaluateTrue) then
                            return "WnW Auto: Refresh Power Word: Shield on Tank (lowest duration)"
                        end
                    end
                end
            else
                -- Otherwise use Penance spam logic
                if Penance:IsReady(Player) then
                    if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Cast Penance on Ally (Auto-Penance)"
                    end
                end
            end
        end

        -- Fallback 1: Try Flash Heal on tank
        if S.FlashHeal:IsReady(Player) then
            if CastTargetIfAlly(S.FlashHeal, Tanks, "min", EvaluateTargetIfHP, EvaluateFlashHealUnderInsight) then
                return "Fallback: Flash Heal under Premonition of Insight"
            end
        end
        
        -- Fallback 2: Try Flash Heal on party members
        if S.FlashHeal:IsReady(Player) then
            if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateFlashHealUnderInsight) then
                return "Fallback: Flash Heal under Premonition of Insight"
            end
        end
        
        -- Fallback 3: If nothing else available, Smite the current enemy target
        if S.Smite:IsReady(Player) then
            if Cast(S.Smite) then
                return "Fallback: Smite enemy under Premonition of Insight"
            end
        end
        
        return false
    end

    -- Handles actions while Premonition of Solace buff is active
    local function PremonitionOfSolaceBuffUp()
        if Settings['PoSusage'] == 'PoSFlashHeal' then
            -- Flash Heal mode - Use Flash Heal for focused healing
            if S.FlashHeal:IsReady(Player) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Flash Heal"
                end
            end
        elseif Settings['PoSusage'] == 'PoSPenance' then
            -- Penance mode - Use Penance on friendly targets
            if Penance:IsReady(Player) then
                if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Penance (Friendly)"
                end
            end
        else -- 'PoSauto' mode
            if HealingEngine:LowestHP() <= 30 and Penance:CooldownRemains() > 2 then
                -- Use Flash Heal for emergency healing instead
                if S.FlashHeal:IsReady(Player) then
                    if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Emergency Flash Heal"
                    end
                end
            end
            -- Auto mode - Select healing spell based on situation
            if Penance:IsReady(Player) then
                -- Default: Cast Penance on a friendly unit
                if CastTargetIfAlly(Penance, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Penance (Friendly)"
                end
            elseif S.FlashHeal:IsReady(Player) then
                -- Use Flash Heal if Penance isn't available
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Flash Heal (Penance unavailable)"
                end
            end
        end
        
        return false
    end

    local function DamageRotation()
        -- Maintain Shadow Word: Pain debuff if it's refreshable on the target.
        if S.ShadowWordPain:IsReady() and Target:DebuffRefreshable(S.ShadowWordPain) then
            if Cast(S.ShadowWordPain) then
                return "SW:P - Maintained"
            end
        end
    
        -- Cast Penance on enemy targets (Combined Voidweaver/Oracle)
        if Penance:IsReady() and (
            Var['AggressivePenance'] or
            S.EntropicRift:IsAvailable() or 
            (not S.EntropicRift:IsAvailable() and (Settings['PWSHP'] >= 100 or HealingEngine:MembersUnderPercentage(Settings['PWSHP']) >= 1))
        ) then
            local specType = S.EntropicRift:IsAvailable() and "Voidweaver" or "Oracle"
            
            if Target:DebuffUp(S.ShadowWordPainDebuff) or S.WealAndWoe:IsAvailable() then
                if Cast(Penance, "Penance Enemy") then
                    return "Cast Penance " .. specType .. " (Target)"
                end
            else
                if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                    return "Cast Penance " .. specType .. " (MO)"
                end
            end
        end
    
        -- Use Void Blast if available.
        if S.VoidBlast:IsReady() then
            if Cast(S.VoidBlast) then
                return "Cast Void Blast"
            end
        end
    
        -- Cast Mind Blast (Entropic Rift).
        if S.MindBlast:IsReady() then
            if Cast(S.MindBlast) then
                return "Cast Mind Blast"
            end
        end

        -- Cast Holy Nova if not under Shadow Covenant and conditions are met.
        if not Player:BuffUp(S.ShadowCovenantBuff) then
            if S.HolyNova:IsReady(Player) and Player:BuffStack(S.RhapsodyBuff) == 20 then
                if EnemiesCount12y >= 3 or Var['AverageHPInRange'] <= 95 then
                    if Cast(S.HolyNova) then
                        return "Cast Holy Nova"
                    end
                end
            end
        end
    
        -- When under Shadow Covenant, prioritize Shadow Word: Death.
        if Player:BuffUp(S.ShadowCovenantBuff) then
            if S.ShadowWordDeath:IsReady() then
                if Cast(S.ShadowWordDeath) then
                    return "Cast SW: Death (Covenant)"
                end
            end
        end
    
        -- Otherwise, use Shadow Word: Death as an execute if conditions are met.
        if S.ShadowWordDeath:IsReady() then
            if EvaluateSWDEntropic(Target) then
                if Cast(S.ShadowWordDeath) then
                    return "Cast SW: Death (Execute, Target)"
                end
            else
                if CastCycle(S.ShadowWordDeath, Enemies40y, EvaluateSWDEntropic) then
                    return "Cast SW: Death (Execute, MO)"
                end
            end
        end
    
        -- Cast Divine Star for AoE damage.
        if S.DivineStar:IsReady() then
            if Cast(DivineStar) then
                return "Cast Divine Star"
            end
        end
    
        -- Cast Halo for AoE damage.
        if S.Halo:IsReady() then
            if Cast(Halo) then
                return "Cast Halo"
            end
        end
    
        -- Cast Smite as a filler ability.
        if S.Smite:IsReady() then
            if Cast(S.Smite) then
                return "Cast Smite"
            end
        end
    
        -- Use Bag Of Tricks for additional burst damage.
        if S.BagofTricks:IsReady() then
            if Cast(S.BagofTricks) then
                return "Cast Bag Of Tricks"
            end
        end
    
        -- Cast Arcane Pulse if it's ready.
        if S.ArcanePulse:IsReady() then
            if Cast(S.ArcanePulse) then
                return "Cast Arcane Pulse"
            end
        end
    
        -- Cast Light's Judgment as an extra damage option.
        if S.LightsJudgment:IsReady() then
            if Cast(S.LightsJudgment) then
                return "Cast Light's Judgment"
            end
        end
    
        -- As a fallback, cycle through Shadow Word: Pain on enemies.
        if S.ShadowWordPain:IsReady() then
            if CastCycle(S.ShadowWordPain, Enemies40y, EvaluatePain) then
                return "Cycle SW:P on Enemies"
            end
        end
    end    

    local function EntropicRiftRotation()
        -- Cast Penance on enemy targets as part of the Entropic Rift rotation.
        if Penance:IsReady() then
            if Target:DebuffUp(S.ShadowWordPainDebuff) then
                if Cast(Penance, "Penance Enemy") then
                    return "Entropic Rift: Cast Penance (Target)"
                end
            else
                if CastCycle(Penance, Enemies40y, EvaluateSwPPenance, nil, "Penance Enemy") then
                    return "Entropic Rift: Cast Penance (MO)"
                end
            end
        end
    
        -- Refresh Shadow Word: Pain on the target if it's about to expire.
        if S.ShadowWordPain:IsReady() and Target:DebuffRefreshable(S.ShadowWordPain) then
            if Cast(S.ShadowWordPain) then
                return "Entropic Rift: Refresh SW:P"
            end
        end
    
        -- Use Void Blast as an additional damage option during Entropic Rift.
        if S.VoidBlast:IsReady() then
            if Cast(S.VoidBlast) then
                return "Entropic Rift: Cast Void Blast"
            end
        end
    end    

    local function Filler()
        -- Attempt to cast Renew on group members.
        if S.Renew:IsReady(Player) then
            if GetSetting('renewMovement', true) then
                -- If the setting requires movement and the player is moving, cast Renew.
                if Player:IsMoving() then
                    if CastCycleAlly(S.Renew, Members, EvaluateRenew) then
                        return "Filler: Renew cast while moving"
                    end
                end
            else
                -- Cast Renew regardless of movement.
                if CastCycleAlly(S.Renew, Members, EvaluateRenew) then
                    return "Filler: Renew cast"
                end
            end
        end
    
        -- Attempt to cast Power Word: Shield on group members.
        if S.PowerWordShield:IsReady(Player) then
            if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWSFiller) then
                return "Filler: Cast Power Word: Shield"
            end
        end
    
        -- Use Flash Heal during combat.
        if S.FlashHeal:IsReady(Player) then
            if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHeal) then
                return "Filler: Flash Heal (combat)"
            end
        end
    
        -- Use Flash Heal out of combat.
        if not Var['IsInCombat'] then
            if S.FlashHeal:IsReady(Player) then
                if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHealOOC) then
                    return "Filler: Flash Heal (OOC)"
                end
            end
        end
    
        -- Attempt to cast Prayer of Mending on group members.
        if S.PrayerofMending:IsReady(Player) then
            if CastCycleAlly(S.PrayerofMending, Members, EvaluatePoM) then
                return "Filler: Cast Prayer of Mending"
            end
        end
    end    

    local function APL()
        -- Var Update
        UpdateVars()

        if Var['TargetIsValid'] or Var['IsInCombat'] then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies40y, false)
            end
            Priest.PiFightRemains = FightRemains
        end

        if MainAddon.Toggle:GetToggle('ForceDPS') then
            local ShouldReturn = DamageRotation();
            if ShouldReturn then
                 return "Force DPS Toggle: " .. ShouldReturn;
            end
        end

        -- Call Ramp Toggle function
        if MainAddon.Toggle:GetToggle("Ramp") then
            local ShouldReturn = RampToggle()
            if ShouldReturn then
                return ShouldReturn
            end
        end
        
        -- Trinkets
        local shouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
        if shouldReturn then
            return shouldReturn
        end  

        -- Potion
        if MainAddon.UseManaPotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end

        if Var['IsInCombat'] then
            -- Pet combo usage
            if Var['TargetIsValid'] or Var['TargetIsValidAndImmune'] then
                if (Settings['bender_x_radiance'] and (Player:PrevGCDP(1, S.PowerWordRadiance) or S.PowerWordRadiance:TimeSinceLastCast() <= 2)) 
                or (Settings['bender_x_premonition'] and not S.EntropicRift:IsAvailable() and (Player:PrevGCDP(1, S.PremonitionOfInsight) or S.PremonitionOfInsight:TimeSinceLastCast() <= 2) 
                    or (Player:PrevGCDP(1, S.PremonitionOfSolace) or S.PremonitionOfSolace:TimeSinceLastCast() <= 2)
                    or (Player:PrevGCDP(1, S.PremonitionOfPiety) or S.PremonitionOfPiety:TimeSinceLastCast() <= 2)) then
                    if Var['Fiend']:IsReady() then
                        if Cast(Var['Fiend']) then
                            return "Pet combo usage"
                        end
                    end
                end
            end
            
            -- Defensives
            local ShouldReturn = Defensives()
            if ShouldReturn then
                return "Defensives: " .. ShouldReturn
            end

            -- Power Infusion
            local ShouldReturn = PowerInfusion()
            if ShouldReturn then
                return "PI: " .. ShouldReturn
            end

            -- // Start Premonition logics \\ --
            -- Premonition of Insight
            if Player:BuffUp(S.PremonitionOfInsight) then
                local ShouldReturn = PremonitionOfInsightBuffUp()
                if ShouldReturn then
                    return "Premonition of Insight: " .. ShouldReturn
                end
            end

            -- Premonition of Solace
            if Player:BuffUp(S.PremonitionOfSolace) then
                local ShouldReturn = PremonitionOfSolaceBuffUp()
                if ShouldReturn then
                    return "Premonition of Solace: " .. ShouldReturn
                end
            end
            -- // End Premonition logics \\ --
            
            -- Healing CDs
            local ShouldReturn = HealingCDs()
            if ShouldReturn then
                return "Healing CDs: " .. ShouldReturn
            end
        end

        if Var['TargetIsValid'] then
			local ShouldReturn = Trinkets();
			if ShouldReturn then
			    return ShouldReturn;
			end
		end

        if Var['InDungeon'] then
            -- Spiteful Apparitions
            if MainAddon.IsCurrentAffix("Spiteful") then
                if S.DominateMind:IsCastable() and not Pet:IsActive() then
                    if M.CastTargetIf(S.DominateMind, Enemies40y, "max", EvaluateSpiteful, IsSpiteful) then
                        return "Dominate Mind - Spiteful"
                    end
                end
            end
        end

        -- Healing Special Mouseover
        -- if GetSetting("whateversetting", false) then
        --     local ShouldReturn = HealingSpecialMO()
        --     if ShouldReturn then
        --         return ShouldReturn
        --     end
        -- end

        -- Healing Special
        local ShouldReturn = HealingSpecial();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Damage Incoming
        local Reason, SpellID = MainAddon:DamageIncoming()
        if Reason == "SOON" then
            MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)

            local ShouldReturn = DamageIncoming();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        -- Entropic Rift
        if S.VoidBlast:IsReady() and #Var['MembersWithAtonement'] >= 2 and (Var['LowestHP'] >= Settings['FlashHealHP_EH'] or Var['LowestHP'] >= Settings['PenanceHP_EH'])  then
            local ShouldReturn = EntropicRiftRotation()
            if ShouldReturn then
                return "Entropic Rift: " .. ShouldReturn
            end
        end

        -- Healing Rotation
        local ShouldReturn = HealingRotation()
        if ShouldReturn then
            return "Healing Rotation: " .. ShouldReturn
        end

        -- Utilities
        local ShouldReturn = Utilities()
        if ShouldReturn then
            return "Utilities: " .. ShouldReturn
        end

        if Var['TargetIsValidAndImmune'] then
            local ShouldReturn = DamageRotation()
            if ShouldReturn then
                return "Damage Rotation: " .. ShouldReturn
            end
        end

        -- Filler
        local ShouldReturn = Filler()
        if ShouldReturn then
            return "Filler: " .. ShouldReturn
        end
    end

    -- Initialize the rotation
    local function Init()
        -- Any initialization code would go here
    end
    
    -- Register the rotation with the addon
    M.SetAPL(256, APL, Init)

    local OldDisciplineCharges
    OldDisciplineCharges = HL.AddCoreOverride("Spell.Charges",
            function(self)
                local BaseCheck = OldDisciplineCharges(self)
                if MainAddon.PlayerSpecID() == 256 then
                    if self == S.PowerWordRadiance then
                        return BaseCheck - num(Player:IsCasting(S.PowerWordRadiance))
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 256)

    -- Override the IsCastable spell method for Discipline-specific logic
    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
        function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            if MainAddon.PlayerSpecID() == 256 then

                -- Check for 'keepradiance' setting - dont use last charge
                if self == S.PowerWordRadiance and GetSetting("keepradiance", false) then
                    if S.PowerWordRadiance:Charges() <= 1 then
                        return false, "Keeping last charge for manual use"
                    end
                end

                if self == S.PowerWordRadiance then
                    if Player:IsCasting(S.PowerWordRadiance) or Player:PrevGCDP(1, S.PowerWordRadiance) then
                        return false, "Already casting it."
                    end
                end

                if self == S.MindBlast then
                    if Player:IsCasting(S.MindBlast) then
                        return false, "Already casting it."
                    end
                end
                
                if self == S.FlashHeal then
                    if Player:IsCasting(S.FlashHeal) then
                        return false, "Already casting it."
                    end
                end

                if self == S.DominateMind then
                    if Player:IsCasting(S.DominateMind) then
                        return false, "Already casting it."
                    end
                end

                if self == S.Halo then
                    if Player:IsCasting(S.Halo) then
                        return false, "Already casting it."
                    end
                end

                if self == S.ShadowHalo then
                    if Player:IsCasting(S.ShadowHalo) then
                        return false, "Already casting it."
                    end
                end

                if self == S.PowerWordShield then
                    if Player:IsCasting(S.Smite) and Player:BuffUp(S.WealandWoeBuff) then
                        return false, "Prevent Overlap."
                    end
                end

                if self == S.Fade then
                    if MainAddon.SpecialCase_FreedomBlacklist() then
                        return false, "Freedom Blacklist"
                    end
                end

                IsInGroup() Player:IsInRaidArea() 
                if self == S.PowerWordShield then
                    if S.WealAndWoe:IsAvailable() and Player:BuffDown(S.WealandWoeBuff) and not S.EntropicRift:IsAvailable() then
                        return false, "Don't use PW:Shield without Weal and Woe."
                    end
                end

                if self == S.Smite then
                    if Player:BuffUp(S.WealandWoeBuff) and Penance:CooldownRemains() <= 0.5 and not S.EntropicRift:IsAvailable() then
                        return false, "Don't use Smite with Weal and Woe."
                    end

                    if Player:BuffUp(S.WealandWoeBuff) and Player:BuffUp(S.PremonitionOfInsight) and not S.EntropicRift:IsAvailable() then
                        return true, "Cast Smite with Weal and Woe and Premonition of Insight."
                    end
                end

                if self ~= S.PowerWordShield and self ~= Penance and self ~= S.PremonitionOfPiety and self ~= S.FlashHeal and self ~= S.Smite and self ~= S.DominateMind then
                    if Player:BuffUp(S.PremonitionOfInsight) then
                        return false, "Only use PW:Shield or Penance with Premonition of Insight."
                    end
                end
            end
            local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            return BaseCheck, Reason
        end,
    256);

    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4, arg5)
        if arg2 == "player" and arg5 then
            if arg5 == 421453 then
                MainAddon.StopCasting = true
                C_Timer.After(2.5,
                function()
                    MainAddon.StopCasting = false
                end)
            end
        end
    end, "UNIT_SPELLCAST_SENT")

    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4)
        if arg2 == "player" and arg4 then
            if arg4 == 421434 then
                MainAddon.StopCasting = false
            end
        end
    end, "UNIT_SPELLCAST_SUCCEEDED")

    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4)
        if arg2 == "player" and arg4 then
            if arg4 == 421453 or arg4 == 421434 then
                MainAddon.StopCasting = false
            end
        end
    end, "UNIT_SPELLCAST_INTERRUPTED", "UNIT_SPELLCAST_FAILED")
end