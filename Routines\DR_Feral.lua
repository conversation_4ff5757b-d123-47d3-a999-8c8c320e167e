function A_103(...)
    -- HR Update: fix(Feral): Use new Item:HasUseDamage() (20/03/25)
    -- REMEMBER: ExcludeNPCListRip ExcludeNPCListFeralFrenzy ExcludeNPCListPrimalWrath
    -- REMEMBER: Funnel()
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    local HL = HeroLibEx
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastAlly = M.CastAlly
    local CastCycleAlly = MainAddon.CastCycleAlly
    local CastPooling = M.CastPooling
    local CastTargetIf = M.CastTargetIf
    local CastCycle = M.CastCycle
    local AoEON = M.AoEON
    local num = M.num
    -- LUA
    local C_Timer = _G['C_Timer']
    local GetTime = _G['GetTime']
    local GetMouseFoci = _G['GetMouseFoci']
    local pairs = pairs
    local math = math
    local mathfloor   = math['floor']
    local mathmax     = math['max']
    local mathmin     = math['min']
    local Delay       = C_Timer.After

    local S = Spell.Druid.Feral
    local I = Item.Druid.Feral

    local OnUseExcludes = {
        I.BestinSlotsMelee:ID(),
    }

    -- Toggle Setting
    MainAddon.Toggle.Special["BearMode"] = {
        Icon = MainAddon.GetTexture(S.BearForm),
        Name = "Bear Mode",
        Description = "Bear Mode.",
        Spec = 103
    }

    MainAddon.Toggle.Special["Funneling"] = {
        Icon = MainAddon.GetTexture(S.FerociousBite),
        Name = "Funneling",
        Description = "Funneling.",
        Spec = 103,
    }
    
    S.AdaptiveSwarm.StanceAura = 0
    S.AdaptiveSwarm.Jumps = 0
    HL:RegisterForEvent(
            function(...)
                local event, targetedunit, auratable = ...
                if auratable.removedAuraInstanceIDs then
                    for i, v in pairs(auratable.removedAuraInstanceIDs) do
                        if v == S.AdaptiveSwarm.StanceAura then
                            if S.AdaptiveSwarm.Jumps < 3 then
                                S.AdaptiveSwarm:StartInFlight()
                            else
                                S.AdaptiveSwarm.Jumps = 0
                            end
                        end
                    end
                end

                if auratable.addedAuras then
                    for i, v in pairs(auratable.addedAuras) do
                        if v.spellId == 391891 then
                            S.AdaptiveSwarm:RemoveInFlight()
                            S.AdaptiveSwarm.StanceAura = v.auraInstanceID
                            S.AdaptiveSwarm.Jumps = S.AdaptiveSwarm.Jumps + 1
                        end

                        if v.spellId == 391889 then
                            S.AdaptiveSwarm:RemoveInFlight()
                            S.AdaptiveSwarm.StanceAura = v.auraInstanceID
                            S.AdaptiveSwarm.Jumps = S.AdaptiveSwarm.Jumps + 1
                        end
                    end
                end
            end,
            "UNIT_AURA"
    )

    ---GUI SETTINGS
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FF7C0A'
    local Config_Table = {
        key = Config_Key,
        title = 'Druid - Feral',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'checkbox', text = ' Disable dispel and low priority kicks while in CDs', icon = S.FerociousBite:ID(), key = 'cdnodispel', default = true },
            { type = 'checkbox', text = ' Sync Convoke with Berserk', key = 'Align2Min', icon = S.Berserk:ID(), default = false },
            { type = 'dropdown',
              text = 'Opener', key = 'opener',
              list = {
                  { text = 'Rake', key = 1 },
                  { text = 'Shred', key = 2 },
              },
              default = 1
            },
            { type = 'checkbox', text = ' Easy Swipe', key = 'lazyswipe', icon = S.Swipe:ID(), default = false },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Survival Instincts', icon = S.SurvivalInstincts:ID(), key = 'survinst', min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'checkspin', text = ' Barkskin', icon = S.Barkskin:ID(), key = 'barkskin', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkspin', text = ' Frenzied Regeneration', icon = S.FrenziedRegeneration:ID(), key = 'FR', min = 1, max = 100, default_spin = 55, default_check = true },
            { type = 'checkspin', text = ' Renewal', icon = S.Renewal:ID(), key = 'renewal', min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'checkbox', text = ' Regrowth Filler', key = 'regrowth_pooling', icon = S.Regrowth:ID(), default = true },
            { type = 'checkspin', text = ' Regrowth (Self)', icon = S.Regrowth:ID(), key = 'regrowth_self', min = 1, max = 100, default_spin = 75, default_check = true },
            { type = 'checkspin', text = ' Regrowth (Party)', icon = S.Regrowth:ID(), key = 'regrowth_party', min = 1, max = 100, default_spin = 50, default_check = true },
            { type = 'dropdown',
              text = ' Regrowth - Targets', key = 'regrowth_targets',
              icon = S.Regrowth:ID(),
              multiselect = true,
              list = {
                  { text = 'Healer', key = 'healer' },
                  { text = 'Tank', key = 'tank' },
                  { text = 'DPS', key = 'dps' },
              },
              default = {
                "healer",
                "tank",
                "dps"
              },
            },
            { type = 'checkspin', text = " Nature's Vigil", icon = S.NaturesVigil:ID(), key = 'nvigil', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkbox', text = " Nature's Vigil: also check party's allies health", icon = S.NaturesVigil:ID(), key = 'nvigil_allies', default = true },
            { type = 'spacer' },
            { type = 'checkbox', text = ' Smart Bear', key = 'smartbear', icon = S.BearForm:ID(), default = true },
            { type = 'spinner', text = ' Smart Bear above key level', key = 'smart_bear_above_key_level', icon = S.BearForm:ID(), min = 1, max = 40, default = 2 },
            { type = 'checkbox', text = ' Combine Bear Form with other defensives', key = 'combine_bearform', icon = S.BearForm:ID(), default = false },
            { type = 'dropdown',
              text = ' Hold Bear Form until', key = 'smartholdbear',
              icon = S.BearForm:ID(),
              width = 205,
              multiselect = true,
              list = {
                  { text = 'Ursine Vigor falls off', key = 'smartholdbear_uv' },
                  { text = 'Frenzied Regeneration falls off', key = 'smartholdbear_fg' },
                  { text = 'Danger is over', key = 'smartholdbear_over' },
              },
              default = {
                  'smartholdbear_uv',
                  'smartholdbear_fg'
              },
            },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'spinner', text = " Skull Bash - Range usage (reload required)", icon = S.SkullBash:ID(), key = 'feralrangekick', min = 5, max = 16, default = 10 },
            { type = 'checkbox', text = ' Prowl when enemies around', icon = 5215, default = true, key = 'autostealth' },
            { type = 'checkbox', text = ' Cat Form (Out of Combat)', icon = 768, key = 'catformooc', default = true },
            { type = 'spacer' },
            { type = 'dropdown',
              text = ' Innervate', key = 'innervate',
              icon = S.Innervate:ID(),
              list = {
                  { text = 'Target', key = 'innervate_target' },
                  { text = 'MouseOver', key = 'innervate_mouseover' },
                  { text = 'Focus', key = 'innervate_focus' },
                  { text = 'None', key = 'innervate_none' },
              },
              default = 'innervate_none'
            },
            { type = 'spinner', text = " Innervate: Healer's Mana threshold", icon = S.Innervate:ID(), key = 'innervate_mana', min = 1, max = 100, default = 60, step = 1 },
            { type = 'spacer' },
            { type = 'dropdown',
              text = ' Rebirth', key = 'autorebirth',
              icon = S.Rebirth:ID(),
              multiselect = true,
              list = {
                  { text = 'Target', key = 'autorebirth_target' },
                  { text = 'MouseOver', key = 'autorebirth_mouseover' },
              },
              default = {
              },
            },
            { type = 'spacer' },
            { type = 'dropdown',
              text = ' Mark of the Wild', key = 'motw_targets',
              icon = S.MarkoftheWild:ID(),
              multiselect = true,
              list = {
                  { text = 'Self', key = 'motw_self' },
                  { text = 'Friends', key = 'motw_friends' },
              },
              default = {
                  'motw_self',
                  'motw_friends'
              },
            },
            { type = 'checkbox', text = "Typhoon only when enemy is under Ursol's Vortex", key = 'typhoon_vortex', icon = S.Typhoon:ID(), default = false },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Rake snapshot tracker', width = 150, callback = function()
                if not _G.WeakAuras then
                    return false
                end
                _G.WeakAuras.Import("!WA:2!9zvqVTrru46grryXHIHgQkfjxJAuQq1K4kQefLk5nDDtb3KO12KwqL1ZUZSENM1ZmAMztI7TAHeCboKFcwCKt(NqpWvwfXVGEGFa9xaVzwNeNu76dEM9nV9BEZ37BEVTqJs9lHlH)ThoIgYzT4PYqY1VWrOuDmxULqt5mvXXyQsKGg0MCGUYnzYbBffPi6cJrSqWTT5uMoyD3nB76jkpLV(rCzFK2x4RP9jtEQGefAH9UEknsQlgerzuvCrhyqxuuzgaSA1ZcXB2Lqe1vcsO2dbGfFOIKezJJSlnctuiDQePjXorCME8dPjs(GYo8eStchH)7dtveFgzpImoqJsim9IE9tt00IEHjiLAPafL1lHi3PUxZTw)7ZUYIbB0XC(6kFbNrEewDrlgwV7kkDYCFed7BcSUJS249dq6UdnZTwDm)Vu2N(EzxPOfbucDps3xF2xgC4FG98pVm4p9fKfbNpsfJW89Fso1VWijPhqIThiioAGPMdTPpdT58coVFHrAEiCYvWRF1c)AFpuIigTqTukECnHS2tX3zqYUdgfrpGG3HI1X)Yhk(8zIoBYuhgWjsEQoHcJB1PDZhTP7CENJN6zcmSxipHl)UfGFJctvAEFJ3IBeLYSIKLVvXYWpjbYNmyaLuN7wKWWtOJNMthGInKdzx((Sw7tfKUIlFkADeyqk4bzBM(vh7MlUhjEOws71dOI)yj5KP)NJgO0G83EC(GV1eiw1PkqCfKgfz4D5gUn3UrNMoPGUnWCsjYSVjydii1XVwLgy3XTLeGj9ATTBZMNySfab9GX(RxVvB)wTR71EYPFtuFYbf(QZD8PrLzCD5hJOS6ymNvwhty2vMICQKVKXrJgNGRy9a4k7ycpeLmfeRvUjnOLony5k1djwB3(ovxPYTU3djA7Jl)J(2rtmDRPWOns2JOba2aY5aiUhuTdWavZTpLJTeKKKZ4N1Y0EaRATv9bsq)vTbrIYxF60EoW3B7hBUHksOe5YTQ6H2fIkZPRyXCsvj(yll73z7huVTRFNw1DA6MDFr5zRM0YusoaEHXKWDdsTcLHktab3Wl6XGtou9BIkiMIjz1omLnrQSy29VOPY4ZTOVhPgInquAIUl3LM8E0qXTpz)N8MNlmS2(PAp7BlBchXhHSWnbIhZXK)6cIpBMLvZNKv7nqHpIKHs(H871)(HigTVPYi7UzFqJxrqkslTKW6PJF)S1YQEe2uFew2QTLkcu8hRgA8ZiTZQ60huknYEM17SFoZpRlmw88we38DvTwijHut8SWiq57zRyPU2IzRDe8yakC3EqrdgoiMq7fRFzaeer0Efhtz5ibUV0yyAiX1MG7kUkThJljtAmzT6kLCjS0xo)gp4bqMKg6RJLevmuayWqtFHwqP11cu2EEb0(cUuptE(4YCWX9lM)MC6HDov(onDnZDrF6YxoVVAdjO)S)jU2oe0U1HCM6ERSIFhnnHQhu2AyoPanrP7JL6ENg8xFgogU6jR)MP2vJiiO16EUUBkpys3(zYW65YWhxPnEMKM(TiTB)UAG924)A4tu4gnsOThLtD4rhZ3W0v(C4kinAq2LM9o)wetfkEydxV6n9xz1H7ZL4DKiXWDMmzCKHqGBpinAbV9nB2lN5x5C2ETZlTC(ZTKYmnJjXZHaMReECEhW1TnqluOa0afUdtZ)InXnMbytAFEQG4toohz86bufkiHGJlPc(6QRUs1vlT3)(K))")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Feral", Config_Color)
    M.SetConfig(103, Config_Table)

    --- ===== Rotation Variables =====
    local VarConvokeCountRemaining, VarZerkCountRemaining
    local VarPotCountRemaining, VarSlot1CountRemaining, VarSlot2CountRemaining
    local VarFirstHoldBerserkCondition, VarSecondHoldBerserkCondition
    local VarHoldBerserk, VarHoldConvoke, VarHoldPot
    local VarBsIncCD = S.BerserkHeartoftheLion:IsAvailable() and 120 or 180
    local VarConvokeCD = S.AshamanesGuidance:IsAvailable() and 60 or 120
    local VarPotCD = 300
    local VarHighestCDRemaining, VarLowestCDRemaining, VarSecondLowestCDRemaining
    local VarRipDuration, VarRipMaxPandemicDuration
    local VarEffectiveEnergy, VarTimeToPool, VarDoTRefreshSoon
    local VarNeedBT, VarProccingBT
    local VarCCCapped, VarRegrowth, VarEasySwipe
    local ComboPoints, ComboPointsDeficit
    local BsInc = S.Incarnation:IsAvailable() and S.Incarnation or S.Berserk
    local IsInMeleeRange, IsInAoERange
    local BossFightRemains = 11111
    local FightRemains = 11111
    local EnemiesMelee, EnemiesCountMelee
    local Enemies8y, EnemiesCount8y

    --- ===== Start Custom =====
    local inCombat, inStealth
    local TargetIsValid
    local inCat = false
    local inDungeon, inRaid
    local prowlProcessing = false
    local TempBlackListSymbioticRelationship = {}
    local BiteFinisher = S.FerociousBite
    
    ---@param TargetUnit Unit
    local function EvaluateREG(TargetUnit)
        return TargetUnit:HealthPercentage() <= GetSetting('regrowth_party_spin', 30)
    end

    -- Exclusion for Rip
    local ExcludeNPCListRip = {
        -- Spiteful Shades
        [174773] = true,
        -- Rotburst Totem - Brackenhide Hollow
        [190381] = true
    }

    -- Exclusion for Primal Wrath
    local ExcludeNPCListPrimalWrath = {
        -- Rotburst Totem - Brackenhide Hollow
        [190381] = true
    }

    -- Exclusion for Feral Frenzy
    local ExcludeNPCListFeralFrenzy = {
        -- Spiteful Shades
        [174773] = true
    }

    ---@param TargetedUnit Unit
    local function EvaluateSymbioticRelationship(TargetedUnit)
        return TargetedUnit:BuffDown(S.SymbioticRelationship) and not TempBlackListSymbioticRelationship[TargetedUnit:UnfilterName()]
    end

    -- Innervate Variables
    local InnervateName = S.Innervate:Name()
    local MacroFocus = '/cast [@focus, nodead, help] ' .. InnervateName
    local MacroMouseOver = '/cast [@mouseover, nodead, help] ' .. InnervateName
    local MacroTarget = '/cast [@target, nodead, help] ' .. InnervateName
    local innervateMode = GetSetting('innervate', 'innervate_none')
    local InnervateValue = "None"
    local StaticInnervateValue = "None"

    local function UpdateMacroInnervate()    
        if innervateMode == 'innervate_target' then
            M.Autobind.ClickedUpdateMacro(InnervateName, MacroTarget)
        elseif innervateMode == 'innervate_mouseover' then
            M.Autobind.ClickedUpdateMacro(InnervateName, MacroMouseOver)
        elseif innervateMode == 'innervate_focus' then
            M.Autobind.ClickedUpdateMacro(InnervateName, MacroFocus)
        end
    end

    local old_OnValueChanged = MainAddon.Interface.usedGUIs.CORE_DruidFeral_CONFIG.elements.innervate.eventListeners.OnValueChanged
    MainAddon.Interface.usedGUIs.CORE_DruidFeral_CONFIG.elements.innervate.eventListeners.OnValueChanged = function(...)
        old_OnValueChanged(...)
        innervateMode = GetSetting('innervate', 'innervate_none')

        local function UpdateInnervate()
            if not Player:AffectingCombat() then
                UpdateMacroInnervate()
                InnervateValue = M.Autobind.MacroValue(InnervateName)
            else
                C_Timer.After(3, UpdateInnervate)
            end
        end
        UpdateInnervate()
        InnervateValue = M.Autobind.MacroValue(InnervateName)
    end

    local function Defensives()
        if inCombat then
            local DefensiveDown = Player:BuffDown(S.Barkskin) and Player:BuffDown(S.UrsineVigorBuff) and Player:BuffDown(S.SurvivalInstincts)
            if GetSetting('nvigil_check', false) then
                if S.NaturesVigil:IsReady(Player) and Player:BuffDown(S.NaturesVigil) then
                    if Player:HealthPercentage() <= GetSetting('nvigil_spin', 30) then
                        if Cast(S.NaturesVigil) then
                            return "Defensive: Nature's Vigil Player"
                        end
                    end
                    if GetSetting('nvigil_allies', false) and Player:IsInParty() then
                        if MainAddon.AllyLowestHP() <= GetSetting('nvigil_spin', 30) then
                            if Cast(S.NaturesVigil) then
                                return "Defensive: Nature's Vigil Ally"
                            end
                        end
                    end
                end
            end
            if GetSetting('survinst_check', false) then
                if S.SurvivalInstincts:IsReady(Player) and DefensiveDown and Player:HealthPercentage() <= GetSetting('survinst_spin', 30) then
                    if Cast(S.SurvivalInstincts) then
                        return 'Defensive: Survival Instincts'
                    end
                end
            end
            local Should, CastID = Player:ShouldUseDefensive()
            if Should then
                if Player:MythicDifficulty() >= GetSetting('smart_bear_above_key_level', 2) or not inDungeon then
                    if GetSetting('smartbear', false) and Player:BuffDown(S.BearForm) and DefensiveDown and S.UrsineVigor:IsAvailable() and S.BearForm:IsReady(Player) then
                        if Cast(S.BearForm) then
                            MainAddon.UI:ShowToast("Defensive", MainAddon.GetSpellInfo(CastID), MainAddon.GetTexture(S.BearForm))
                            return 'Defensive: Smart Bear'
                        end
                    end
                end
            end
            if GetSetting('barkskin_check', false) then
                if S.Barkskin:IsReady(Player) and DefensiveDown and Player:HealthPercentage() <= GetSetting('barkskin_spin', 30) then
                    if Cast(S.Barkskin) then
                        return 'Defensive: Barkskin'
                    end
                end
            end
            if GetSetting('renewal_check', false) then
                if S.Renewal:IsReady(Player) and Player:HealthPercentage() <= GetSetting('renewal_spin', 30) then
                    if Cast(S.Renewal) then
                        return 'Defensive: Renewal'
                    end
                end
            end
            if GetSetting('FR_check', false) then
                if Player:HealthPercentage() <= GetSetting('FR_spin', 30) then
                    if S.FrenziedRegeneration:IsReady(Player) then
                        if Cast(S.FrenziedRegeneration) then
                            return 'Defensive: Frenzied Regeneration'
                        end
                    else
                        if not S.FrenziedRegeneration:IsBlocked() and S.FrenziedRegeneration:Charges() < 1 and S.HeartoftheWild:IsReady(Player) then
                            if Cast(S.HeartoftheWild) then
                                return 'Defensive: Heart of the Wild'
                            end
                        end
                    end
                end
            end
        end

        if S.Regrowth:IsReady(Player) and Player:BuffUp(S.PredatorySwiftnessBuff) then
            if GetSetting('regrowth_self_check', false) and S.Regrowth:IsReady(Player) and Player:HealthPercentage() <= GetSetting('regrowth_self_spin', 30) then
                if CastAlly(S.Regrowth, Player) then
                    return "regrowth defensive";
                end
            end

            if GetSetting('regrowth_party_check', false) then
                local Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()
                local Reg_Targets = GetSetting('regrowth_targets', {})
                if HL.Utils.tableCount(Reg_Targets) > 0 then
                    local Reg_Tanks = Reg_Targets['tank']
                    local Reg_Healers = Reg_Targets['healer']
                    local Reg_DPS = Reg_Targets['dps']

                    if Reg_Tanks and Tanks then
                        if CastCycleAlly(S.Regrowth, Tanks, EvaluateREG) then
                            return "Regrowth Tanks"
                        end
                    end

                    if Reg_Healers and Healers then
                        if CastCycleAlly(S.Regrowth, Healers, EvaluateREG) then
                            return "Regrowth Healers"
                        end
                    end

                    if Reg_DPS and Damagers then
                        if CastCycleAlly(S.Regrowth, Damagers, EvaluateREG) then
                            return "Regrowth DPS"
                        end
                    end
                end
            end
        end
    end

    local function Utilities()
        local smartholdbear = GetSetting('smartholdbear', {})
        if Player:BuffUp(S.BearForm) then
            if smartholdbear['smartholdbear_fg'] then
                if S.FrenziedRegeneration:IsReady(Player) and Player:BuffDown(S.FrenziedRegenerationBuff) and Player:HealthPercentage() <= 80 then
                    if Cast(S.FrenziedRegeneration) then
                        return "Utilities: Frenzied Regeneration";
                    end
                end
                if S.Ironfur:IsReady(Player) and Player:BuffDown(S.IronfurBuff) and Player:HealthPercentage() <= 80 then
                    if Cast(S.Ironfur) then
                        return "Utilities: Ironfur";
                    end
                end
            end
            local Should = Player:ShouldUseDefensive()
            if not Player:PrevGCD(1, S.IncapacitatingRoar) then
                if smartholdbear['smartholdbear_uv'] and Player:BuffUp(S.UrsineVigorBuff) then
                    return
                elseif smartholdbear['smartholdbear_fg'] and Player:BuffUp(S.FrenziedRegenerationBuff) then
                    return
                elseif smartholdbear['smartholdbear_over'] and Should then
                    return
                end
            end
        end

        if (GetSetting('catformooc', false) or inCombat) and S.CatForm:IsReady(Player) and Player:BuffDown(S.CatForm) and (TargetIsValid and not S.FluidForm:IsAvailable() or Player:IsMoving()) then
            if Cast(S.CatForm) then
                return "cat form";
            end
        end

        if inCombat then
            local autorebith = GetSetting('autorebirth', {})
            if S.Rebirth:IsReady(MouseOver) and autorebith['autorebirth_mouseover'] then
                local GetMouseFociCache = GetMouseFoci()
                ---@class Frame
                local MouseFocus = GetMouseFociCache[1]

                local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                    if MouseOver:EvaluateRebirth() then
                        if Cast(S.Rebirth) then
                            MainAddon.UI:ShowToast("Rebirth", MouseOver:Name(), MainAddon.GetTexture(S.Rebirth))
                            return "Rebirth MouseOver"
                        end
                    end
                end
            end
            if S.Rebirth:IsReady() and autorebith['autorebirth_target'] then
                if Target:EvaluateRebirth() then
                    if Cast(S.Rebirth) then
                        MainAddon.UI:ShowToast("Rebirth", Target:Name(), MainAddon.GetTexture(S.Rebirth))
                        return "Rebirth Target"
                    end
                end
            end
            
            if innervateMode == 'innervate_target' then
                StaticInnervateValue = MacroTarget
            elseif innervateMode == 'innervate_mouseover' then
                StaticInnervateValue = MacroMouseOver
            elseif innervateMode == 'innervate_focus' then
                StaticInnervateValue = MacroFocus
            else
                StaticInnervateValue = "None"
            end

            if InnervateValue == StaticInnervateValue then
                local innervate = GetSetting('innervate', 'innervate_none')
                local innervate_mana = GetSetting('innervate_mana', 50)
                if S.Innervate:IsReady(MouseOver) and innervate == 'innervate_mouseover' then
                    local GetMouseFociCache = GetMouseFoci()
                    ---@class Frame
                    local MouseFocus = GetMouseFociCache[1]

                    local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                    if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                        if MouseOver:IsAHealer() and not MouseOver:IsDeadOrGhost() and Player:IsFriend(MouseOver) and MouseOver:IsInPartyOrRaid()
                        and MouseOver:ManaPercentage() <= innervate_mana and MouseOver:PowerType() == 0 and MouseOver:BuffDown(S.Innervate, true) 
                        and (MouseOver:IsCasting() or MouseOver:IsChanneling()) then
                            if Cast(S.Innervate) then
                                return "Innervate MouseOver"
                            end
                        end
                    end
                end
                if S.Innervate:IsReady(Focus) and innervate == 'innervate_focus' then
                    if Focus:IsAHealer() and not Focus:IsDeadOrGhost() and Player:IsFriend(Focus) and Focus:IsInPartyOrRaid()
                    and Focus:ManaPercentage() <= innervate_mana and Focus:PowerType() == 0 and Focus:BuffDown(S.Innervate, true) 
                    and (Focus:IsCasting() or Focus:IsChanneling()) then
                        if Cast(S.Innervate) then
                            return "Innervate Focus"
                        end
                    end
                end
                if S.Innervate:IsReady(Target) and innervate == 'innervate_target' then
                    if Target:IsAHealer() and not Target:IsDeadOrGhost() and Player:IsFriend(Target) and Target:IsInPartyOrRaid()
                    and Target:ManaPercentage() <= innervate_mana and Target:PowerType() == 0 and Target:BuffDown(S.Innervate, true) 
                    and (Target:IsCasting() or Target:IsChanneling()) then
                        if Cast(S.Innervate) then
                            return "Innervate Target"
                        end
                    end
                end
            end
        end

        local motw = GetSetting('motw_targets', {})
        if S.MarkoftheWild:IsReady(Player)
        and (motw['motw_self'] and Player:BuffDown(S.MarkoftheWild, true)
        or motw['motw_friends'] and M.GroupBuffMissing(S.MarkoftheWild) and (inDungeon or inRaid))
        and (Player:StealthDown(true, true) or #Player:GetEnemiesInRange(40) == 0) then
            if Cast(S.MarkoftheWild) then
                return 'Utilities: Mark of the Wild'
            end
        end

        -- Symbiotic Relationship handling (tank priority)
        if S.SymbioticRelationship:IsReady(Player) and not inCombat then
            local Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()
            if inDungeon and Tanks and #Tanks > 0 then
                -- In dungeon pick the tank
                local tank = Tanks[1]
                if tank
                    and tank:Exists()
                    and not tank:IsDeadOrGhost()
                    and tank:IsInRange(40)
                    and EvaluateSymbioticRelationship(tank)  -- needed to avoid double casts
                then
                    if CastAlly(S.SymbioticRelationship, tank) then
                        return 'Symbiotic Relationship (Dungeon)'
                    end
                end
        
            elseif inRaid and Tanks and #Tanks > 0 then
                -- In raids pick lowest hp tank
                local lowestTankHP, lowestTankUnit = HealingEngine:LowestHP(true, 40, Tanks, "Tank")
                if lowestTankUnit
                    and lowestTankUnit:Exists()
                    and not lowestTankUnit:IsDeadOrGhost()
                    and lowestTankUnit:IsInRange(40)
                    and EvaluateSymbioticRelationship(lowestTankUnit)  -- needed to avoid double casts
                then
                    if CastAlly(S.SymbioticRelationship, lowestTankUnit) then
                        return 'Symbiotic Relationship (Raid)'
                    end
                end
            end
        end
    end

    local function Bear()
        -- Frenzied Regeneration
        if S.FrenziedRegeneration:IsReady() and Player:BuffDown(S.FrenziedRegenerationBuff) and Player:HealthPercentage() <= 90 then
            if Cast(S.FrenziedRegeneration) then
                return "Bear: Frenzied Regeneration";
            end
        end

        -- Ironfur
        if S.Ironfur:IsReady() and Player:BuffDown(S.IronfurBuff) then
            if Cast(S.Ironfur) then
                return "Bear: Ironfur";
            end
        end

        -- Brutal Slash
        if S.BrutalSlash:IsReady() then
            if Cast(S.BrutalSlash) then
                return "Bear: Brutal Slash";
            end
        end

        -- Thrash
        if S.ThrashBear:IsReady() and Target:DebuffDown(S.ThrashDebuffBear) then
            if Cast(S.ThrashBear) then
                return "Bear: Thrash";
            end
        end

        -- Swipe
        if S.SwipeBear:IsReady() and EnemiesCount8y >= 2 then
            if Cast(S.SwipeBear) then
                return "Bear: Swipe";
            end
        end

        -- Mangle
        if S.Mangle:IsReady() then
            if Cast(S.Mangle) then
                return "Bear: Mangle";
            end
        end

        -- Swipe
        if S.SwipeBear:IsReady() then
            if Cast(S.SwipeBear) then
                return "Bear: Swipe 2";
            end
        end

        -- Thrash
        if S.ThrashBear:IsReady() then
            if Cast(S.ThrashBear) then
                return "Bear: Thrash 2";
            end
        end
    end
    --- ===== End Custom =====

    --- ===== Trinket Variables =====
    ---@type Item
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    local VarTrinket1Spell, VarTrinket2Spell
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CastTime, VarTrinket2CastTime
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarTrinket1Buffs, VarTrinket2Buffs
    local VarTrinket1Duration, VarTrinket2Duration
    local VarTrinket1Sync, VarTrinket2Sync
    local VarTrinketPriority
    local VarTrinketFailures = 0
    local function SetTrinketVariables()
      local T1, T2 = Player:GetTrinketData(OnUseExcludes)
    
      -- If we don't have trinket items, try again in 5 seconds.
      if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
        VarTrinketFailures = VarTrinketFailures + 1
        Delay(5, function()
            SetTrinketVariables()
          end
        )
        return
      end
    
      ---@type Item
      Trinket1 = T1.Object
      ---@type Item
      Trinket2 = T2.Object
    
      VarTrinket1ID = Trinket1:ID()
      VarTrinket2ID = Trinket2:ID()
    
      VarTrinket1Spell = T1.Spell
      VarTrinket1Range = T1.Range
      VarTrinket1CastTime = T1.CastTime
      VarTrinket2Spell = T2.Spell
      VarTrinket2Range = T2.Range
      VarTrinket2CastTime = T2.CastTime
    
      VarTrinket1CD = T1.Cooldown
      VarTrinket2CD = T2.Cooldown
    
      VarTrinket1Ex = T1.Excluded
      VarTrinket2Ex = T2.Excluded
    
      VarTrinket1Buffs = Trinket1:HasUseBuff() and VarTrinket1ID ~= I.ImperfectAscendancySerum:ID() and VarTrinket1ID ~= I.OvinaxsMercurialEgg:ID() and VarTrinket1ID ~= I.ConcoctionKissofDeath:ID()
      VarTrinket2Buffs = Trinket2:HasUseBuff() and VarTrinket2ID ~= I.ImperfectAscendancySerum:ID() and VarTrinket2ID ~= I.OvinaxsMercurialEgg:ID() and VarTrinket2ID ~= I.ConcoctionKissofDeath:ID()
    
      -- Note: Using value >0 to avoid divide by zero errors.
      VarTrinket1Duration = (Trinket1:BuffDuration() > 0) and Trinket1:BuffDuration() or 1
      VarTrinket2Duration = (Trinket2:BuffDuration() > 0) and Trinket2:BuffDuration() or 1
    
      VarTrinket1Sync = 0.5
      if (S.ConvoketheSpirits:IsAvailable() and not S.AshamanesGuidance:IsAvailable() and VarTrinket1Buffs and (VarTrinket1CD % 120 == 0 or 120 % VarTrinket1CD == 0)) or (not (S.ConvoketheSpirits:IsAvailable() and not S.AshamanesGuidance:IsAvailable()) and VarTrinket1Buffs and (VarTrinket1CD % 180 == 0 or 180 % VarTrinket1CD == 0 or VarTrinket1CD % 120 == 0 or 120 % VarTrinket1CD == 0)) then
        VarTrinket1Sync = 1
      end
      VarTrinket2Sync = 0.5
      if (S.ConvoketheSpirits:IsAvailable() and not S.AshamanesGuidance:IsAvailable() and VarTrinket2Buffs and (VarTrinket2CD % 120 == 0 or 120 % VarTrinket2CD == 0)) or (not (S.ConvoketheSpirits:IsAvailable() and not S.AshamanesGuidance:IsAvailable()) and VarTrinket2Buffs and (VarTrinket2CD % 180 == 0 or 180 % VarTrinket2CD == 0 or VarTrinket2CD % 120 == 0 or 120 % VarTrinket2CD == 0)) then
        VarTrinket2Sync = 1
      end
    
      VarTrinketPriority = 1
      if not VarTrinket1Buffs and VarTrinket2Buffs or VarTrinket2Buffs and ((VarTrinket2CD / VarTrinket2Duration) * (VarTrinket2Sync)) > ((VarTrinket1CD / VarTrinket1Duration) * (VarTrinket1Sync)) then
        VarTrinketPriority = 2
      end
    end
    SetTrinketVariables()

    --- ===== Event Registration =====
    HL:RegisterForEvent(function()
        BsInc = S.Incarnation:IsAvailable() and S.Incarnation or S.Berserk
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
    
    HL:RegisterForEvent(function()
        VarTrinketFailures = 0
        SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")
    
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")
    
    HL:RegisterForEvent(function()
        S.AdaptiveSwarm:RegisterInFlightEffect(391889)
        S.AdaptiveSwarm:RegisterInFlight()
    end, "LEARNED_SPELL_IN_TAB")
    S.AdaptiveSwarm:RegisterInFlightEffect(391889)
    S.AdaptiveSwarm:RegisterInFlight()

    --- ===== PMultiplier Registrations =====
    local function ComputeRakePMultiplier()
        local result = 1
        if Player:StealthUp(true, true)
        or Player:BuffUp(S.SuddenAmbushBuff) 
        or (S.Prowl:TimeSinceLastRemovedOnPlayer() < 1 or S.SuddenAmbushBuff:TimeSinceLastRemovedOnPlayer() < 1) then
            result = 1.6
        end
        return result
    end
    S.Rake:RegisterPMultiplier(S.RakeDebuff, ComputeRakePMultiplier)

    local function ComputeRipPMultiplier()
        local Mult = 1
        Mult = Player:BuffUp(S.BloodtalonsBuff) and Mult * 1.25 or Mult
        Mult = S.DreadfulBleeding:IsAvailable() and Mult * 1.2 or Mult
        Mult = Player:HasTier("TWW1", 4) and Mult * 1.08 or Mult
        Mult = S.LionsStrength:IsAvailable() and Mult * 1.15 or Mult
        Mult = Player:BuffUp(S.TigersFury) and Mult * 1.15 or Mult
        return Mult
    end
    S.Rip:RegisterPMultiplier(S.RipDebuff, ComputeRipPMultiplier)
    
    --- ===== Helper Functions =====
    local BtTriggers = {
        S.Rake,
        S.LIMoonfire,
        S.ThrashCat,
        S.BrutalSlash,
        S.Swipe,
        S.Shred,
        S.FeralFrenzy,
    }
    
    local function DebuffRefreshAny(ThisEnemies, ThisSpell)
        for _, ThisEnemy in pairs(ThisEnemies) do
            if ThisEnemy:DebuffRefreshable(ThisSpell) then
                return true
            end
        end
        return false
    end
    
    local function BTBuffUp(Trigger)
        if not S.Bloodtalons:IsAvailable() then return false end
        return Trigger:TimeSinceLastCast() < mathmin(5, S.BloodtalonsBuff:TimeSinceLastAppliedOnPlayer())
    end
    
    local function BTBuffDown(Trigger)
        return not BTBuffUp(Trigger)
    end
    
    local function CountActiveBtTriggers()
        local ActiveTriggers = 0
        for i = 1, #BtTriggers do
            if BTBuffUp(BtTriggers[i]) then ActiveTriggers = ActiveTriggers + 1 end
        end
        return ActiveTriggers
    end
    
    local function TicksGainedOnRefresh(Spell, Tar)
        if not Tar then Tar = Target end
        local AddedDuration = 0
        local MaxDuration = 0
        -- Added TickTime variable, as Rake and Moonfire don't have tick times in DBC
        local TickTime = 0
        if Spell == S.RipDebuff then
          AddedDuration = (4 + ComboPoints * 4)
          MaxDuration = 31.2
          TickTime = Spell:TickTime()
        else
          AddedDuration = Spell:BaseDuration()
          MaxDuration = Spell:MaxDuration()
          TickTime = Spell:TickTime()
        end
      
        local OldTicks = Tar:DebuffTicksRemain(Spell)
        local OldTime = Tar:DebuffRemains(Spell)
        local NewTime = AddedDuration + OldTime
        if NewTime > MaxDuration then NewTime = MaxDuration end
        local NewTicks = NewTime / TickTime
        if not OldTicks then OldTicks = 0 end
        local TicksAdded = NewTicks - OldTicks
        return TicksAdded
    end
      
    local function HighestTTD(enemies)
        if not enemies then return 0 end
        local HighTTD = 0
        local HighTTDTar = nil
        for _, enemy in pairs(enemies) do
          local TTD = enemy:TimeToDie()
          if TTD > HighTTD then
            HighTTD = TTD
            HighTTDTar = enemy
          end
        end
        return HighTTD, HighTTDTar
    end

    --- ===== CastTargetIf Filter Functions =====
    local function EvaluateTargetIfFilterAdaptiveSwarm(TargetUnit)
        -- target_if=max:(1+dot.adaptive_swarm_damage.stack)*dot.adaptive_swarm_damage.stack<3*time_to_die
        return (1 + TargetUnit:DebuffStack(S.AdaptiveSwarmDebuff)) * num(TargetUnit:DebuffStack(S.AdaptiveSwarmDebuff) < 3) * TargetUnit:TimeToDie()
    end
    
    local function EvaluateTargetIfFilterBloodseeker(TargetUnit)
        -- target_if=max:dot.bloodseeker_vines.ticking
        return TargetUnit:DebuffRemains(S.BloodseekerVinesDebuff)
    end
    
    local function EvaluateTargetIfFilterLIMoonfire(TargetUnit)
        -- target_if=max:ticks_gained_on_refresh
        return TicksGainedOnRefresh(S.LIMoonfireDebuff, TargetUnit)
    end
    
    local function EvaluateTargetIfFilterRakeAoEBuilder(TargetUnit)
        -- target_if=max:ticks_gained_on_refresh
        return TicksGainedOnRefresh(S.RakeDebuff, TargetUnit)
    end
    
    local function EvaluateTargetIfFilterRakeMain(TargetUnit)
        -- target_if=max:refreshable+(persistent_multiplier>dot.rake.pmultiplier)
        return num(TargetUnit:DebuffRefreshable(S.RakeDebuff)) + num(Player:PMultiplier(S.Rake) > TargetUnit:PMultiplier(S.Rake))
    end
    
    local function EvaluateTargetIfFilterTTD(TargetUnit)
        -- target_if=min:target.time_to_die
        return TargetUnit:TimeToDie()
    end
    
    --- ===== CastTargetIf Condition Functions =====
    local function EvaluateTargetIfAdaptiveSwarm(TargetUnit)
        -- if=buff.cat_form.up&dot.adaptive_swarm_damage.stack<3&talent.unbridled_swarm.enabled&spell_targets.swipe_cat>1&dot.rip.ticking
        -- Note: Everything but stack count and rip check handled before CastTargetIf call
        return TargetUnit:DebuffStack(S.AdaptiveSwarmDebuff) < 3 and TargetUnit:DebuffUp(S.RipDebuff)
    end
    
    local function EvaluateTargetIfBrutalSlashAoeBuilder(TargetUnit)
        -- if=!(variable.need_bt&buff.bt_swipe.up)&(cooldown.brutal_slash.full_recharge_time<4|time_to_die<4|raid_event.adds.remains<4)
        return not (VarNeedBT or BTBuffUp(S.Swipe)) and (S.BrutalSlash:FullRechargeTime() < 4 or TargetUnit:TimeToDie() < 4 or FightRemains < 4)
    end
    
    local function EvaluateTargetIfLIMoonfireRefreshable(TargetUnit)
        -- if=refreshable
        return TargetUnit:DebuffRefreshable(S.LIMoonfireDebuff)
    end
    
    local function EvaluateTargetIfPrimalWrath(TargetUnit)
        -- if=spell_targets.primal_wrath>1&((dot.primal_wrath.remains<6.5&!buff.bs_inc.up|dot.primal_wrath.refreshable)|(!talent.rampant_ferocity.enabled&(spell_targets.primal_wrath>1&!dot.bloodseeker_vines.ticking&!buff.ravage.up|spell_targets.primal_wrath>6+talent.ravage)))
        return (TargetUnit:DebuffRemains(S.RipDebuff) < 6.5 and Player:BuffDown(BsInc) or TargetUnit:DebuffRefreshable(S.RipDebuff)) or (not S.RampantFerocity:IsAvailable() and (EnemiesCount8y > 1 and TargetUnit:DebuffDown(S.BloodseekerVinesDebuff) and Player:BuffDown(S.RavageBuffFeral) or EnemiesCount8y > 6 + num(S.Ravage:IsAvailable())))
    end
    
    local function EvaluateTargetIfRakeRefreshable(TargetUnit)
        -- if=refreshable
        return TargetUnit:DebuffRefreshable(S.RakeDebuff)
    end
    
    --- ===== CastCycle Condition Functions =====
    local function EvaluateCycleAdaptiveSwarm(TargetUnit)
        -- target_if=dot.adaptive_swarm_damage.stack<3&(!dot.adaptive_swarm_damage.ticking|dot.adaptive_swarm_damage.remains<2),if=!action.adaptive_swarm_damage.in_flight&(spell_targets=1|!talent.unbridled_swarm)&(dot.rip.ticking|hero_tree.druid_of_the_claw)
        return (TargetUnit:DebuffStack(S.AdaptiveSwarmDebuff) < 3 and (TargetUnit:DebuffDown(S.AdaptiveSwarmDebuff) or TargetUnit:DebuffRemains(S.AdaptiveSwarmDebuff) < 2)) and (TargetUnit:DebuffUp(S.RipDebuff) or Player:HeroTreeID() == 21)
    end
    
    local function EvaluateCycleMoonfire(TargetUnit)
        -- target_if=refreshable
        return TargetUnit:DebuffRefreshable(S.LIMoonfireDebuff)
    end
    
    local function EvaluateCycleRake(TargetUnit)
        -- target_if=!dot.rake.ticking
        return TargetUnit:DebuffDown(S.RakeDebuff)
    end
    
    local function EvaluateCycleRakeRefreshable(TargetUnit)
        -- target_if=refreshable
        return TargetUnit:DebuffRefreshable(S.RakeDebuff)
    end
    
    local function EvaluateCycleRakeAoeBuilder(TargetUnit)
        -- target_if=dot.rake.pmultiplier<1.6
        return TargetUnit:PMultiplier(S.Rake) < 1.6
    end
    
    local function EvaluateCycleRip(TargetUnit)
        -- target_if=refreshable
        return TargetUnit:DebuffRefreshable(S.RipDebuff)
        and not ExcludeNPCListRip[TargetUnit:NPCID()]
    end
    
    local function EvaluateCycleRip2(TargetUnit)
        -- target_if=refreshable,if=(!talent.primal_wrath|spell_targets=1)&(buff.bloodtalons.up|!talent.bloodtalons)&(buff.tigers_fury.up|dot.rip.remains<cooldown.tigers_fury.remains)&(remains<fight_remains|remains<4&buff.ravage.up)
        -- Note: (!talent.primal_wrath|spell_targets=1)&(buff.bloodtalons.up|!talent.bloodtalons) checked before CastCycle.
        return TargetUnit:DebuffRefreshable(S.RipDebuff) and (Player:BuffUp(S.TigersFury) or TargetUnit:DebuffRemains(S.RipDebuff) < S.TigersFury:CooldownRemains()) and (TargetUnit:DebuffRemains(S.RipDebuff) < FightRemains or TargetUnit:DebuffRemains(S.RipDebuff) < 4 and Player:BuffUp(S.RavageBuffFeral))
        and not ExcludeNPCListRip[TargetUnit:NPCID()]
    end

    -- APL Functions
    local function Precombat()
        -- Manually added: wild_charge
        if S.WildCharge:IsReady() and (not Target:IsInRange(8)) then
            if Cast(S.WildCharge) then
                return "wild_charge precombat 6";
            end
        end
        if S.TigersFury:IsReady()
        and (Player:EnergyDeficit() > 35 or ComboPoints == 5) then
            if Cast(S.TigersFury) then 
                return "tigers_fury precombat"; 
            end
        end
        if GetSetting('opener', 1) == 1 then
            if S.Rake:IsReady() then
                if Cast(S.Rake) then
                    return "rake precombat";
                end
            end
        elseif GetSetting('opener', 1) == 2 then
            if S.Shred:IsReady() then
                if Cast(S.Shred) then
                    return "shred precombat";
                end
            end
        end
    end
            
    local function AoeBuilder()
        -- variable,name=proccing_bt,op=set,value=variable.need_bt
        VarProccingBT = VarNeedBT
        -- thrash_cat,if=refreshable&!talent.thrashing_claws&!(variable.need_bt&buff.bt_thrash.up)
        if S.ThrashCat:IsReady() and (Target:DebuffRefreshable(S.ThrashCatDebuff) and not S.ThrashingClaws:IsAvailable() and not (VarNeedBT and BTBuffUp(S.ThrashCat))) then
          if Cast(S.ThrashCat) then return "thrash aoe_builder 2"; end
        end
        -- brutal_slash,target_if=min:time_to_die,if=(cooldown.brutal_slash.full_recharge_time<4|time_to_die<4|raid_event.adds.remains<4|(buff.bs_inc.up&spell_targets>=3-hero_tree.druid_of_the_claw))&!(variable.need_bt&buff.bt_swipe.up&(buff.bs_inc.down|spell_targets<3-hero_tree.druid_of_the_claw))
        if S.BrutalSlash:IsReady() and ((S.BrutalSlash:FullRechargeTime() < 4 or FightRemains < 4 or (Player:BuffUp(BsInc) and EnemiesCount8y >= 3 - num(Player:HeroTreeID() == 21))) and not (VarNeedBT and BTBuffUp(S.Swipe) and (Player:BuffDown(BsInc) or EnemiesCount8y < 3 - num(Player:HeroTreeID() == 21)))) then
          if Cast(S.BrutalSlash) then return "brutal_slash aoe_builder 4"; end
        end
        -- swipe_cat,target_if=min:time_to_die,if=talent.wild_slashes&(time_to_die<4|raid_event.adds.remains<4|buff.bs_inc.up&spell_targets>=3-hero_tree.druid_of_the_claw)&!(variable.need_bt&buff.bt_swipe.up&(buff.bs_inc.down|spell_targets<3-hero_tree.druid_of_the_claw))
        if S.Swipe:IsReady() and (S.WildSlashes:IsAvailable() and (FightRemains < 4 or Player:BuffUp(BsInc) and EnemiesCount8y >= 3 - num(Player:HeroTreeID() == 21)) and not (VarNeedBT and BTBuffUp(S.Swipe) and (Player:BuffDown(BsInc) or EnemiesCount8y < 3 - num(Player:HeroTreeID() == 21)))) then
          if Cast(S.Swipe) then return "swipe aoe_builder 5"; end
        end
        -- swipe_cat,if=time_to_die<4|(talent.wild_slashes&spell_targets.swipe_cat>4&!(variable.need_bt&buff.bt_swipe.up))
        if S.Swipe:IsReady() and (FightRemains < 4 or (S.WildSlashes:IsAvailable() and EnemiesCount8y > 4 and not (VarNeedBT and BTBuffUp(S.Swipe)))) then
          if Cast(S.Swipe) then return "swipe aoe_builder 6"; end
        end
        -- prowl,target_if=dot.rake.refreshable|dot.rake.pmultiplier<1.4,if=!(variable.need_bt&buff.bt_rake.up)&action.rake.ready&gcd.remains=0&!buff.sudden_ambush.up&!variable.cc_capped
        -- Note: Skipping cycling and putting target_if into main condition.
        if S.Prowl:IsReady() and not Player:StealthUp(false, true) and ((not (VarNeedBT and BTBuffUp(S.Rake)) and S.Rake:IsReady() and Player:BuffDown(S.SuddenAmbushBuff) and not VarCCCapped) and (DebuffRefreshAny(EnemiesMelee, S.RakeDebuff) or Target:PMultiplier(S.Rake) < 1.4)) then
          if Cast(S.Prowl) then return "prowl aoe_builder 8"; end
        end
        -- shadowmeld,target_if=dot.rake.refreshable|dot.rake.pmultiplier<1.4,if=!(variable.need_bt&buff.bt_rake.up)&action.rake.ready&!buff.sudden_ambush.up&!buff.prowl.up&!variable.cc_capped
        -- Note: Skipping cycling and putting target_if into main condition.
        if S.Shadowmeld:IsReady() and not Player:StealthUp(false, true) and ((not (VarNeedBT and BTBuffUp(S.Rake)) and S.Rake:IsReady() and Player:BuffDown(S.SuddenAmbushBuff) and Player:BuffDown(S.Prowl) and not VarCCCapped) and (DebuffRefreshAny(EnemiesMelee, S.RakeDebuff) or Target:PMultiplier(S.Rake) < 1.4)) then
          if Cast(S.Shadowmeld) then return "shadowmeld aoe_builder 10"; end
        end
        -- rake,target_if=refreshable,if=talent.doubleclawed_rake&!(variable.need_bt&buff.bt_rake.up)&!variable.cc_capped
        if S.Rake:IsReady() and (S.DoubleClawedRake:IsAvailable() and not (VarNeedBT and BTBuffUp(S.Rake)) and not VarCCCapped) then
          if CastCycle(S.Rake, EnemiesMelee, EvaluateCycleRakeRefreshable) then return "rake aoe_builder 12"; end
        end
        -- swipe_cat,if=talent.wild_slashes&spell_targets.swipe_cat>2&!(variable.need_bt&buff.bt_swipe.up)
        if S.Swipe:IsReady() and (S.WildSlashes:IsAvailable() and EnemiesCount8y > 2 and not (VarNeedBT and BTBuffUp(S.Swipe))) then
          if Cast(S.Swipe) then return "swipe aoe_builder 14"; end
        end
        -- rake,target_if=max:dot.rake.ticking,if=!dot.rake.ticking&hero_tree.wildstalker
        if S.Rake:IsReady() and (Player:HeroTreeID() == 22) then
            if CastCycle(S.Rake, EnemiesMelee, EvaluateCycleRake) then return "rake aoe_builder 15"; end
        end
        -- moonfire_cat,target_if=refreshable,if=!(variable.need_bt&buff.bt_moonfire.up)&!variable.cc_capped
        if S.LIMoonfire:IsReady() and (not (VarNeedBT and BTBuffUp(S.LIMoonfireDebuff)) and not VarCCCapped) then
          if CastCycle(S.LIMoonfire, Enemies8y, EvaluateCycleMoonfire) then return "moonfire_cat aoe_builder 16"; end
        end
        -- rake,target_if=refreshable,if=!(variable.need_bt&buff.bt_rake.up)&!variable.cc_capped
        if S.Rake:IsReady() and (S.DoubleClawedRake:IsAvailable() and not (VarNeedBT and BTBuffUp(S.Rake)) and not VarCCCapped) then
          if CastCycle(S.Rake, EnemiesMelee, EvaluateCycleRakeRefreshable) then return "rake aoe_builder 18"; end
        end
        -- brutal_slash,if=!(variable.need_bt&buff.bt_swipe.up)
        if S.BrutalSlash:IsReady() and (not (VarNeedBT and BTBuffUp(S.Swipe))) then
          if Cast(S.BrutalSlash) then return "brutal_slash aoe_builder 20"; end
        end
        -- swipe_cat,if=!(variable.need_bt&buff.bt_swipe.up)
        if S.Swipe:IsReady() and (not (VarNeedBT and BTBuffUp(S.Swipe))) then
          if Cast(S.Swipe) then return "swipe aoe_builder 22"; end
        end
        -- shred,if=!buff.sudden_ambush.up&!variable.easy_swipe&!(variable.need_bt&buff.bt_shred.up)
        if S.Shred:IsReady() and (Player:BuffDown(S.SuddenAmbushBuff) and not VarEasySwipe and not (VarNeedBT and BTBuffUp(S.Shred))) then
          if Cast(S.Shred) then return "shred aoe_builder 24"; end
        end
        -- thrash_cat,if=!talent.thrashing_claws&!(variable.need_bt&buff.bt_thrash.up)
        if S.ThrashCat:IsReady() and (not S.ThrashingClaws:IsAvailable() and not (VarNeedBT and BTBuffUp(S.ThrashCat))) then
          if Cast(S.ThrashCat) then return "thrash aoe_builder 26"; end
        end
        -- rake,target_if=max:ticks_gained_on_refresh,if=talent.doubleclawed_rake&buff.sudden_ambush.up&variable.need_bt&buff.bt_rake.down
        if S.Rake:IsReady() and (S.DoubleClawedRake:IsAvailable() and Player:BuffUp(S.SuddenAmbushBuff) and VarNeedBT and BTBuffDown(S.Rake)) then
          if CastTargetIf(S.Rake, Enemies8y, "max", EvaluateTargetIfFilterRakeAoEBuilder, nil) then return "rake aoe_builder 28"; end
        end
        -- moonfire_cat,target_if=max:ticks_gained_on_refresh,if=variable.need_bt&buff.bt_moonfire.down
        if S.LIMoonfire:IsReady() and (VarNeedBT and BTBuffDown(S.LIMoonfire)) then
          if CastTargetIf(S.LIMoonfire, Enemies8y, "max", EvaluateTargetIfFilterLIMoonfire, nil) then return "moonfire_cat aoe_builder 30"; end
        end
        -- rake,target_if=max:ticks_gained_on_refresh,if=buff.sudden_ambush.up&variable.need_bt&buff.bt_rake.down
        if S.Rake:IsReady() and (Player:BuffUp(S.SuddenAmbushBuff) and VarNeedBT and BTBuffDown(S.Rake)) then
          if CastTargetIf(S.Rake, Enemies8y, "max", EvaluateTargetIfFilterRakeAoEBuilder, nil) then return "rake aoe_builder 32"; end
        end
        -- shred,if=variable.need_bt&buff.bt_shred.down&!variable.easy_swipe
        if S.Shred:IsReady() and (VarNeedBT and BTBuffDown(S.Shred) and not VarEasySwipe) then
          if Cast(S.Shred) then return "shred aoe_builder 34"; end
        end
        -- rake,target_if=dot.rake.pmultiplier<1.6,if=variable.need_bt&buff.bt_rake.down
        if S.Rake:IsReady() and (VarNeedBT and BTBuffDown(S.Rake)) then
          if CastCycle(S.Rake, EnemiesMelee, EvaluateCycleRakeAoeBuilder) then return "rake aoe_builder 36"; end
        end
        -- thrash_cat,if=variable.need_bt&buff.bt_shred.down
        if S.ThrashCat:IsReady() and (VarNeedBT and BTBuffDown(S.Shred)) then
          if Cast(S.ThrashCat) then return "thrash aoe_builder 38"; end
        end
    end
      
    local function Builder()
        -- variable,name=proccing_bt,op=set,value=variable.need_bt
        VarProccingBT = VarNeedBT
        -- prowl,if=gcd.remains=0&energy>=35&!buff.sudden_ambush.up&(dot.rake.refreshable|dot.rake.pmultiplier<1.4)&!(variable.need_bt&buff.bt_rake.up)&buff.tigers_fury.up&!buff.shadowmeld.up
        if S.Prowl:IsReady() and not Player:StealthUp(false, true) and (Player:Energy() >= 35 and Player:BuffDown(S.SuddenAmbushBuff) and (Target:DebuffRefreshable(S.RakeDebuff) or Target:PMultiplier(S.Rake) < 1.4) and not (VarNeedBT and BTBuffUp(S.Rake)) and Player:BuffUp(S.TigersFury)) then
          if Cast(S.Prowl) then return "prowl builder 2"; end
        end
        -- shadowmeld,if=gcd.remains=0&energy>=35&!buff.sudden_ambush.up&(dot.rake.refreshable|dot.rake.pmultiplier<1.4)&!(variable.need_bt&buff.bt_rake.up)&buff.tigers_fury.up&!buff.prowl.up
        if S.Shadowmeld:IsReady() and not Player:StealthUp(false, true) and (Player:Energy() >= 35 and Player:BuffDown(S.SuddenAmbushBuff) and (Target:DebuffRefreshable(S.RakeDebuff) or Target:PMultiplier(S.Rake) < 1.4) and not (VarNeedBT and BTBuffUp(S.Rake)) and Player:BuffUp(S.TigersFury)) then
          if Cast(S.Shadowmeld) then return "shadowmeld builder 4"; end
        end
        -- rake,if=((refreshable&persistent_multiplier>=dot.rake.pmultiplier|dot.rake.remains<3.5)|buff.sudden_ambush.up&persistent_multiplier>dot.rake.pmultiplier)&!(variable.need_bt&buff.bt_rake.up)&(hero_tree.wildstalker|!buff.bs_inc.up)
        if S.Rake:IsReady() and (((Target:DebuffRefreshable(S.RakeDebuff) and Player:PMultiplier(S.Rake) >= Target:PMultiplier(S.Rake) or Target:DebuffRemains(S.RakeDebuff) < 3.5) or Player:BuffUp(S.SuddenAmbushBuff) and Player:PMultiplier(S.Rake) > Target:PMultiplier(S.Rake)) and not (VarNeedBT and BTBuffUp(S.Rake)) and (Player:HeroTreeID() == 22 or Player:BuffDown(BsInc))) then
          if Cast(S.Rake) then return "rake builder 6"; end
        end
        -- shred,if=buff.sudden_ambush.up&buff.bs_inc.up
        if S.Shred:IsReady() and (Player:BuffUp(S.SuddenAmbushBuff) and Player:BuffUp(BsInc)) then
          if Cast(S.Shred) then return "shred builder 8"; end
        end
        -- brutal_slash,if=cooldown.brutal_slash.full_recharge_time<4&!(variable.need_bt&buff.bt_swipe.up)
        if S.BrutalSlash:IsReady() and (S.BrutalSlash:FullRechargeTime() < 4 and not (VarNeedBT and BTBuffUp(S.Swipe))) then
          if Cast(S.BrutalSlash) then return "brutal_slash builder 10"; end
        end
        -- moonfire_cat,if=refreshable
        if S.LIMoonfire:IsReady() and (Target:DebuffRefreshable(S.LIMoonfireDebuff)) then
          if Cast(S.LIMoonfire) then return "moonfire_cat builder 12"; end
        end
        -- thrash_cat,if=refreshable&!talent.thrashing_claws&!buff.bs_inc.up
        if S.ThrashCat:IsReady() and (Target:DebuffRefreshable(S.ThrashCatDebuff) and not S.ThrashingClaws:IsAvailable() and Player:BuffDown(BsInc)) then
          if Cast(S.ThrashCat) then return "thrash builder 14"; end
        end
        -- shred,if=buff.clearcasting.react&!(variable.need_bt&buff.bt_shred.up)
        if S.Shred:IsReady() and (Player:BuffUp(S.Clearcasting) and not (VarNeedBT and BTBuffUp(S.Shred))) then
          if Cast(S.Shred) then return "shred builder 16"; end
        end
        -- pool_resource,wait=0.2,if=variable.dot_refresh_soon&energy.deficit>70&!variable.need_bt&!buff.bs_inc.up&cooldown.tigers_fury.remains>3
        -- TODO
        -- brutal_slash,if=!(variable.need_bt&buff.bt_swipe.up)
        if S.BrutalSlash:IsReady() and (not (VarNeedBT and BTBuffUp(S.Swipe))) then
          if Cast(S.BrutalSlash) then return "brutal_slash builder 18"; end
        end
        -- shred,if=!(variable.need_bt&buff.bt_shred.up)
        if S.Shred:IsReady() and (not (VarNeedBT and BTBuffUp(S.Shred))) then
          if Cast(S.Shred) then return "shred builder 20"; end
        end
        -- thrash_cat,if=refreshable&!talent.thrashing_claws
        if S.ThrashCat:IsReady() and (Target:DebuffRefreshable(S.ThrashCatDebuff) and not S.ThrashingClaws:IsAvailable()) then
          if Cast(S.ThrashCat) then return "thrash builder 22"; end
        end
        -- swipe_cat,if=variable.need_bt&buff.bt_swipe.down
        if S.Swipe:IsReady() and (VarNeedBT and BTBuffDown(S.Swipe)) then
          if Cast(S.Swipe) then return "swipe builder 24"; end
        end
        -- rake,if=variable.need_bt&buff.bt_rake.down&persistent_multiplier>=dot.rake.pmultiplier
        if S.Rake:IsReady() and (VarNeedBT and BTBuffDown(S.Rake) and Player:PMultiplier(S.Rake) >= Target:PMultiplier(S.Rake)) then
          if Cast(S.Rake) then return "rake builder 26"; end
        end
        -- moonfire_cat,if=variable.need_bt&buff.bt_moonfire.down
        if S.LIMoonfire:IsReady() and (VarNeedBT and BTBuffDown(S.LIMoonfire)) then
          if Cast(S.LIMoonfire) then return "moonfire_cat builder 28"; end
        end
        -- thrash_cat,if=variable.need_bt&buff.bt_thrash.down
        if S.ThrashCat:IsReady() and (VarNeedBT and BTBuffDown(S.ThrashCat)) then
          if Cast(S.ThrashCat) then return "thrash builder 30"; end
        end
    end

    local function Cooldown()
        -- Note: Lines below check for cooldown.bestinslots.remains>20. Not having the item would return 0, but we likely want an infinite value instead.
        local BISCDRemains = I.BestinSlotsMelee:IsEquipped() and I.BestinSlotsMelee:CooldownRemains() or 999

        -- use_item,slot=trinket1,if=trinket.1.has_use_damage&(trinket.2.cooldown.remains>20&cooldown.bestinslots.remains>20|!trinket.2.has_use_buff&cooldown.bestinslots.remains>20|cooldown.tigers_fury.remains<25&cooldown.tigers_fury.remains>20)|fight_remains<5
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (Trinket1:HasUseDamage() and (Trinket2:CooldownRemains() > 20 and I.BestinSlotsMelee:CooldownRemains() > 20 or not VarTrinket2Buffs and I.BestinSlotsMelee:CooldownRemains() > 20 or S.TigersFury:CooldownRemains() < 25 and S.TigersFury:CooldownRemains() > 20) or BossFightRemains < 5) then
            if Cast(Trinket1) then return "Generic use_item for "..Trinket1:Name().." cooldown 2"; end
        end
        -- use_item,slot=trinket2,if=trinket.2.has_use_damage&(trinket.1.cooldown.remains>20&cooldown.bestinslots.remains>20|!trinket.1.has_use_buff&cooldown.bestinslots.remains>20|cooldown.tigers_fury.remains<25&cooldown.tigers_fury.remains>20)|fight_remains<5
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (Trinket2:HasUseDamage() and (Trinket1:CooldownRemains() > 20 and I.BestinSlotsMelee:CooldownRemains() > 20 or not VarTrinket1Buffs and I.BestinSlotsMelee:CooldownRemains() > 20 or S.TigersFury:CooldownRemains() < 25 and S.TigersFury:CooldownRemains() > 20) or BossFightRemains < 5) then
            if Cast(Trinket2) then return "Generic use_item for "..Trinket2:Name().." cooldown 4"; end
        end
        -- incarnation,if=buff.tigers_fury.up&!variable.holdBerserk
        if S.Incarnation:IsReady() and (Player:BuffUp(S.TigersFury) and not VarHoldBerserk) then
          if Cast(S.Incarnation) then return "incarnation cooldown 6"; end
        end
        -- BS/Convoke sync
        if GetSetting("Align2Min", false) then
            if S.Berserk:IsReady() and S.ConvoketheSpirits:CooldownUp() and (Player:BuffUp(S.TigersFury) or S.TigersFury:CooldownRemains() >= 5) then
                if Cast(S.Berserk) then return "berserk sync cooldown 7"; end
            end
        else
            -- berserk,if=buff.tigers_fury.up&!variable.holdBerserk
            if S.Berserk:IsReady() and (Player:BuffUp(S.TigersFury) and not VarHoldBerserk) then
                if Cast(S.Berserk) then return "berserk cooldown 8"; end
            end
        end
        -- berserking,if=buff.bs_inc.up
        if S.Berserking:IsReady() and (Player:BuffUp(BsInc)) then
          if Cast(S.Berserking) then return "berserking cooldown 10"; end
        end
        -- use_items
        local ItemToUse, _, ItemRange = Player:GetUseableItems(OnUseExcludes, nil, true)
        if ItemToUse and ItemToUse:IsReady() then
            if Cast(ItemToUse) then return "Generic use_item for " .. ItemToUse:Name() .. " cooldown 14"; end
        end
        -- use_item,slot=trinket1,use_off_gcd=1,if=(time>10|buff.bs_inc.up)&trinket.1.has_use_buff&(cooldown.tigers_fury.remains>=25|(trinket.1.is.treacherous_transmitter|trinket.1.is.imperfect_ascendancy_serum)&cooldown.tigers_fury.remains<2)&(buff.potion.up|variable.slot1CountRemaining!=variable.potCountRemaining)&(cooldown.bs_inc.remains<5&!variable.holdBerserk|cooldown.convoke_the_spirits.remains<10&!variable.holdConvoke|variable.lowestCDremaining>trinket.1.cooldown.duration|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&(variable.highestCDremaining+3)>trinket.1.cooldown.duration|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.bs_inc.remains<?cooldown.convoke_the_spirits.remains)>trinket.1.cooldown.duration|variable.slot1CountRemaining=variable.potCountRemaining-1&buff.potion.up|trinket.2.has_use_buff&(variable.secondLowestCDremaining>trinket.1.cooldown.duration&variable.lowestCDremaining>trinket.2.cooldown.remains|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&variable.highestCDremaining>trinket.2.cooldown.remains|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.convoke_the_spirits.remains<?cooldown.bs_inc.remains)>trinket.2.cooldown.remains))
        local Potion = MainAddon.UsePotion() and MainAddon.FindPotion() 
        local PotionUp = Potion and Potion:TimeSinceLastCast() < 30
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and ((HL.CombatTime() > 10 or Player:BuffUp(BsInc)) and Trinket1:HasUseBuff() and (S.TigersFury:CooldownRemains() >= 25 or (VarTrinket1ID == I.TreacherousTransmitter:ID() or VarTrinket1ID == I.ImperfectAscendancySerum:ID()) and S.TigersFury:CooldownRemains() < 2) and (PotionUp or VarSlot1CountRemaining ~= VarPotCountRemaining) and (BsInc:CooldownRemains() < 5 and not VarHoldBerserk or S.ConvoketheSpirits:CooldownRemains() < 10 and not VarHoldConvoke or VarLowestCDRemaining > VarTrinket1CD or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and (VarHighestCDRemaining + 3) > VarTrinket1CD or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(BsInc:CooldownRemains(), S.ConvoketheSpirits:CooldownRemains()) > VarTrinket1CD or VarSlot1CountRemaining  == VarPotCountRemaining - 1 and PotionUp or VarTrinket2Buffs and (VarSecondLowestCDRemaining > VarTrinket1CD and VarLowestCDRemaining > Trinket2:CooldownRemains() or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and VarHighestCDRemaining > Trinket2:CooldownRemains() or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains()) > Trinket2:CooldownRemains()))) then
            if Cast(Trinket1) then return "trinket1 cooldown 16"; end
        end
        -- use_item,slot=trinket2,use_off_gcd=1,if=(time>10|buff.bs_inc.up)&trinket.2.has_use_buff&(cooldown.tigers_fury.remains>=25|(trinket.2.is.treacherous_transmitter|trinket.2.is.imperfect_ascendancy_serum)&cooldown.tigers_fury.remains<2)&(buff.potion.up|variable.slot2CountRemaining!=variable.potCountRemaining)&(cooldown.bs_inc.remains<5&!variable.holdBerserk|cooldown.convoke_the_spirits.remains<10&!variable.holdConvoke|variable.lowestCDremaining>trinket.2.cooldown.duration|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&(variable.highestCDremaining+3)>trinket.2.cooldown.duration|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.bs_inc.remains<?cooldown.convoke_the_spirits.remains)>trinket.2.cooldown.duration|variable.slot1CountRemaining=variable.potCountRemaining-1&buff.potion.up|trinket.1.has_use_buff&(variable.secondLowestCDremaining>trinket.2.cooldown.duration&variable.lowestCDremaining>trinket.1.cooldown.remains|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&variable.highestCDremaining>trinket.1.cooldown.remains|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.convoke_the_spirits.remains<?cooldown.bs_inc.remains)>trinket.1.cooldown.remains))
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and ((HL.CombatTime() > 10 or Player:BuffUp(BsInc)) and Trinket2:HasUseBuff() and (S.TigersFury:CooldownRemains() >= 25 or (VarTrinket2ID == I.TreacherousTransmitter:ID() or VarTrinket2ID == I.ImperfectAscendancySerum:ID()) and S.TigersFury:CooldownRemains() < 2) and (PotionUp or VarSlot2CountRemaining ~= VarPotCountRemaining) and (BsInc:CooldownRemains() < 5 and not VarHoldBerserk or S.ConvoketheSpirits:CooldownRemains() < 10 and not VarHoldConvoke or VarLowestCDRemaining > VarTrinket2CD or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and (VarHighestCDRemaining + 3) > VarTrinket2CD or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(BsInc:CooldownRemains(), S.ConvoketheSpirits:CooldownRemains()) > VarTrinket2CD or VarSlot1CountRemaining  == VarPotCountRemaining - 1 and PotionUp or VarTrinket1Buffs and (VarSecondLowestCDRemaining > VarTrinket2CD and VarLowestCDRemaining > Trinket1:CooldownRemains() or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and VarHighestCDRemaining > Trinket1:CooldownRemains() or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains()) > Trinket1:CooldownRemains()))) then
            if Cast(Trinket2) then return "trinket2 cooldown 18"; end
        end
        -- use_item,slot=trinket1,if=fight_remains<=20
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (BossFightRemains <= 20) then
            if Cast(Trinket1) then return "trinket1 cooldown 20"; end
        end
        -- use_item,slot=trinket2,if=fight_remains<=20
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (BossFightRemains <= 20) then
            if Cast(Trinket2) then return "trinket2 cooldown 22"; end
        end
        -- Custom Trinket non-OnUse
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and not VarTrinket1Buffs then
            if Cast(Trinket1) then return "trinket1 cooldown 20.5"; end
        end
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and not VarTrinket2Buffs then
            if Cast(Trinket2) then return "trinket2 cooldown 22.5"; end
        end
        -- use_item,name=bestinslots,use_off_gcd=1,if=(time>10|buff.bs_inc.up)&cooldown.tigers_fury.remains>=25&(cooldown.bs_inc.remains<5&!variable.holdBerserk|cooldown.convoke_the_spirits.remains<10&!variable.holdConvoke|variable.lowestCDremaining>cooldown.bestinslots.duration|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&(variable.highestCDremaining+3)>cooldown.bestinslots.duration|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.bs_inc.remains<?cooldown.convoke_the_spirits.remains)>cooldown.bestinslots.duration|trinket.2.has_use_buff&((variable.secondLowestCDremaining>cooldown.bestinslots.duration|variable.secondLowestCDremaining>trinket.1.cooldown.duration)&variable.lowestCDremaining>trinket.2.cooldown.remains|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&variable.highestCDremaining>trinket.2.cooldown.remains|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.convoke_the_spirits.remains<?cooldown.bs_inc.remains)>trinket.2.cooldown.remains)|trinket.1.has_use_buff&((variable.secondLowestCDremaining>cooldown.bestinslots.duration|variable.secondLowestCDremaining>trinket.2.cooldown.duration)&variable.lowestCDremaining>trinket.1.cooldown.remains|variable.zerkCountRemaining=1&variable.convokeCountRemaining=1&variable.potCountRemaining=1&variable.highestCDremaining>trinket.1.cooldown.remains|variable.zerkCountRemaining=variable.convokeCountRemaining&variable.zerkCountRemaining!=variable.potCountRemaining&(cooldown.convoke_the_spirits.remains<?cooldown.bs_inc.remains)>trinket.1.cooldown.remains))
        if I.BestinSlotsMelee:IsEquippedAndReady() and ((HL.CombatTime() > 10 or Player:BuffUp(BsInc)) and S.TigersFury:CooldownRemains() >= 25 and (BsInc:CooldownRemains() < 5 and not VarHoldBerserk or S.ConvoketheSpirits:CooldownRemains() < 10 and not VarHoldConvoke or VarLowestCDRemaining > 120 or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and (VarHighestCDRemaining + 3) > 120 or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(BsInc:CooldownRemains(), S.ConvoketheSpirits:CooldownRemains()) > 120 or VarTrinket2Buffs and ((VarSecondLowestCDRemaining > 120 or VarSecondLowestCDRemaining > VarTrinket1CD) and VarLowestCDRemaining > Trinket2:CooldownRemains() or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and VarHighestCDRemaining > Trinket2:CooldownRemains() or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains()) > Trinket2:CooldownRemains()) or VarTrinket1Buffs and ((VarSecondLowestCDRemaining > 120 or VarSecondLowestCDRemaining > VarTrinket2CD) and VarLowestCDRemaining > Trinket1:CooldownRemains() or VarZerkCountRemaining == 1 and VarConvokeCountRemaining == 1 and VarPotCountRemaining == 1 and VarHighestCDRemaining > Trinket1:CooldownRemains() or VarZerkCountRemaining == VarConvokeCountRemaining and VarZerkCountRemaining ~= VarPotCountRemaining and mathmax(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains()) > Trinket1:CooldownRemains()))) then
            if Cast(I.BestinSlotsMelee) then return "bestinslots cooldown 24"; end
        end
        -- use_item,name=bestinslots,use_off_gcd=1,if=fight_remains<=20
        if I.BestinSlotsMelee:IsEquippedAndReady() and (BossFightRemains <= 20) then
            if Cast(I.BestinSlotsMelee) then return "bestinslots cooldown 26"; end
        end
        -- do_treacherous_transmitter_task,if=buff.tigers_fury.up|fight_remains<22
        -- feral_frenzy,if=combo_points<=1+buff.bs_inc.up&(buff.tigers_fury.up|!talent.savage_fury|!hero_tree.wildstalker|fight_remains<cooldown.tigers_fury.remains)
        if S.FeralFrenzy:IsReady() and (ComboPoints <= 1 + num(Player:BuffUp(BsInc)) and (Player:BuffUp(S.TigersFury) or not S.SavageFury:IsAvailable() or Player:HeroTreeID() ~= 22 or BossFightRemains < S.TigersFury:CooldownRemains())) 
        and not ExcludeNPCListFeralFrenzy[Target:NPCID()] then
          if Cast(S.FeralFrenzy) then return "feral_frenzy cooldown 28"; end
        end
        -- convoke_the_spirits-under-berserk
        if GetSetting("Align2Min", false) then
            if S.ConvoketheSpirits:IsReady() and Player:BuffUp(BsInc) then
                if Cast(S.ConvoketheSpirits) then return "convoke_the_spirits sync 30"; end
            end
        else
            -- convoke_the_spirits,if=fight_remains<5|buff.bs_inc.up&buff.bs_inc.remains<5-talent.ashamanes_guidance|buff.tigers_fury.up&!variable.holdConvoke&(combo_points<=4|buff.bs_inc.up&combo_points<=3)
            if S.ConvoketheSpirits:IsReady() and (BossFightRemains < 5 or Player:BuffUp(BsInc) and Player:BuffRemains(BsInc) < 5 - num(S.AshamanesGuidance:IsAvailable()) or Player:BuffUp(S.TigersFury) and not VarHoldConvoke and (ComboPoints <= 4 or Player:BuffUp(BsInc) and ComboPoints <= 3)) then
                if Cast(S.ConvoketheSpirits) then return "convoke_the_spirits cooldown 30"; end
            end
        end
    end

    local function Finisher()
        -- primal_wrath,target_if=max:dot.bloodseeker_vines.ticking,if=spell_targets.primal_wrath>1&((dot.primal_wrath.remains<6.5&!buff.bs_inc.up|dot.primal_wrath.refreshable)|(!talent.rampant_ferocity.enabled&(spell_targets.primal_wrath>1&!dot.bloodseeker_vines.ticking&!buff.ravage.up|spell_targets.primal_wrath>6+talent.ravage)))
        if S.PrimalWrath:IsReady() and (EnemiesCount8y > 1
        and EvaluateTargetIfPrimalWrath(Target)) 
        and not ExcludeNPCListPrimalWrath[Target:NPCID()] then
          if Cast(S.PrimalWrath) then return "primal_wrath finisher 2"; end
        end
        -- rip,target_if=refreshable,if=(!talent.primal_wrath|spell_targets=1)&(buff.bloodtalons.up|!talent.bloodtalons)&(buff.tigers_fury.up|dot.rip.remains<cooldown.tigers_fury.remains)&(remains<fight_remains|remains<4&buff.ravage.up)
        if S.Rip:IsReady() and ((not S.PrimalWrath:IsAvailable() or EnemiesCountMelee == 1) and (Player:BuffUp(S.BloodtalonsBuff) or not S.Bloodtalons:IsAvailable())) then
          if CastCycle(S.Rip, EnemiesMelee, EvaluateCycleRip2) then return "rip finisher 4"; end
        end
        -- pool_resource,for_next=1
        -- ferocious_bite,max_energy=1,target_if=max:dot.bloodseeker_vines.ticking,if=!buff.bs_inc.up
        -- TODO: Determine a way to do both pool_resource and target_if together.
        if BiteFinisher:IsReady(nil, nil, nil, nil, nil, nil, true) and (Player:BuffDown(BsInc)) then
          if CastPooling(BiteFinisher, Player:EnergyTimeToX(50)) then return "ferocious_bite finisher 6"; end
        end
        -- ferocious_bite,target_if=max:dot.bloodseeker_vines.ticking
        if BiteFinisher:IsReady() then
          if Cast(BiteFinisher) then return "ferocious_bite finisher 8"; end
        end
    end

    local function Variables()
        -- variable,name=convokeCountRemaining,value=floor(((fight_remains-variable.convoke_cd)%cooldown.convoke_the_spirits.duration)+(fight_remains>cooldown.convoke_the_spirits.remains))
        local ConvokeCD = S.AshamanesGuidance:IsAvailable() and 60 or 120
        VarConvokeCountRemaining = mathfloor(((FightRemains - VarConvokeCD) / ConvokeCD) + num(FightRemains > S.ConvoketheSpirits:CooldownRemains()))
        -- variable,name=zerkCountRemaining,value=floor(((fight_remains-variable.bs_inc_cd)%cooldown.bs_inc.duration)+(fight_remains>cooldown.bs_inc.remains))
        local BsIncCD = S.BerserkHeartoftheLion:IsAvailable() and 120 or 180
        VarZerkCountRemaining = mathfloor(((FightRemains - VarBsIncCD) / BsIncCD) + num(FightRemains > BsInc:CooldownRemains()))
        -- variable,name=potCountRemaining,value=floor(((fight_remains-variable.pot_cd)%cooldown.potion.duration)+(fight_remains>cooldown.potion.remains))
        local PotionSelected = MainAddon.UsePotion() and MainAddon.FindPotion()
        local PotCDRemains = PotionSelected and PotionSelected:CooldownRemains() or 0
        VarPotCountRemaining = mathfloor(((FightRemains - VarPotCD) / 300) + num(FightRemains > PotCDRemains))
        -- variable,name=slot1CountRemaining,value=floor(((fight_remains-trinket.1.cooldown.remains-10)%trinket.1.cooldown.duration)+(fight_remains>trinket.1.cooldown.remains))
        if VarTrinket1CD > 0 then
            VarSlot1CountRemaining = mathfloor(((FightRemains - Trinket1:CooldownRemains() - 10) / VarTrinket1CD) + num(FightRemains > Trinket1:CooldownRemains()))
        else
            VarSlot1CountRemaining = 0
        end
        -- variable,name=slot2CountRemaining,value=floor(((fight_remains-trinket.2.cooldown.remains-10)%trinket.2.cooldown.duration)+(fight_remains>trinket.2.cooldown.remains))
        if VarTrinket2CD > 0 then
            VarSlot2CountRemaining = mathfloor(((FightRemains - Trinket2:CooldownRemains() - 10) / VarTrinket2CD) + num(FightRemains > Trinket2:CooldownRemains()))
        else
            VarSlot2CountRemaining = 0
        end
        -- variable,name=firstHoldBerserkCondition,value=variable.zerkCountRemaining=1&(variable.convokeCountRemaining=1&cooldown.convoke_the_spirits.remains>10|variable.potCountRemaining=1&cooldown.potion.remains)
        VarFirstHoldBerserkCondition = VarZerkCountRemaining == 1 and (VarConvokeCountRemaining == 1 and S.ConvoketheSpirits:CooldownRemains() > 10 or VarPotCountRemaining == 1 and PotCDRemains > 0)
        -- variable,name=secondHoldBerserkCondition,value=cooldown.convoke_the_spirits.remains>20&variable.convokeCountRemaining=variable.zerkCountRemaining&variable.zerkCountRemaining=floor(((fight_remains-variable.convoke_cd)%cooldown.bs_inc.duration)+(fight_remains>cooldown.convoke_the_spirits.remains))
        VarSecondHoldBerserkCondition = S.ConvoketheSpirits:CooldownRemains() > 20 and VarConvokeCountRemaining == VarZerkCountRemaining and VarZerkCountRemaining == mathfloor(((FightRemains - VarConvokeCD) / BsIncCD) + num(FightRemains > S.ConvoketheSpirits:CooldownRemains()))
        -- variable,name=holdBerserk,value=variable.firstHoldBerserkCondition|variable.secondHoldBerserkCondition
        VarHoldBerserk = VarFirstHoldBerserkCondition or VarSecondHoldBerserkCondition
        -- variable,name=holdConvoke,value=variable.convokeCountRemaining=1&variable.zerkCountRemaining=1&!buff.bs_inc.up
        VarHoldConvoke = VarConvokeCountRemaining == 1 and VarZerkCountRemaining == 1 and Player:BuffDown(BsInc)
        -- variable,name=holdPot,value=variable.potCountRemaining=floor(((fight_remains-variable.bs_inc_cd)%cooldown.potion.duration)+(fight_remains>cooldown.bs_inc.remains))
        VarHoldPot = VarPotCountRemaining == mathfloor(((FightRemains - VarBsIncCD) / 300) + num(FightRemains > BsInc:CooldownRemains()))
        -- variable,name=bs_inc_cd,value=cooldown.bs_inc.remains+10
        VarBsIncCD = BsInc:CooldownRemains() + 10
        -- variable,name=convoke_cd,value=cooldown.convoke_the_spirits.remains+10
        VarConvokeCD = S.ConvoketheSpirits:CooldownRemains() + 10
        -- variable,name=pot_cd,value=cooldown.potion.remains+25
        VarPotCD = PotCDRemains + 25
        -- variable,name=highestCDremaining,value=cooldown.convoke_the_spirits.remains<?cooldown.bs_inc.remains<?cooldown.potion.remains
        VarHighestCDRemaining = mathmax(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains(), PotCDRemains)
        -- variable,name=lowestCDremaining,value=cooldown.convoke_the_spirits.remains>?cooldown.bs_inc.remains>?cooldown.potion.remains
        VarLowestCDRemaining = mathmin(S.ConvoketheSpirits:CooldownRemains(), BsInc:CooldownRemains(), PotCDRemains)
        -- variable,name=secondLowestCDremaining,op=setif,condition=cooldown.convoke_the_spirits.remains>cooldown.bs_inc.remains,value=cooldown.convoke_the_spirits.remains>?cooldown.potion.remains,value_else=cooldown.bs_inc.remains>?cooldown.potion.remains
        if S.ConvoketheSpirits:CooldownRemains() > BsInc:CooldownRemains() then
          VarSecondLowestCDRemaining = mathmin(S.ConvoketheSpirits:CooldownRemains(), PotCDRemains)
        else
          VarSecondLowestCDRemaining = mathmin(BsInc:CooldownRemains(), PotCDRemains)
        end
        -- variable,name=rip_max_pandemic_duration,value=((4+(4*combo_points))*(1-(0.2*talent.circle_of_life_and_death))*(1+(0.25*talent.veinripper)))*0.3
        VarRipMaxPandemicDuration = ((4 + (4 * ComboPoints)) * (1 - (0.2 * num(S.CircleofLifeandDeath:IsAvailable()))) * (1 + (0.25 * num(S.Veinripper:IsAvailable())))) * 0.3
        -- variable,name=rip_duration,value=((4+(4*combo_points))*(1-(0.2*talent.circle_of_life_and_death))*(1+(0.25*talent.veinripper)))+(variable.rip_max_pandemic_duration>?dot.rip.remains)
        VarRipDuration = ((4 + (4 * ComboPoints)) * (1 - (0.2 * num(S.CircleofLifeandDeath:IsAvailable()))) * (1 + (0.25 * num(S.Veinripper:IsAvailable())))) + mathmin(VarRipMaxPandemicDuration, Target:DebuffRemains(S.RipDebuff))
        -- variable,name=effective_energy,op=set,value=energy+(40*buff.clearcasting.stack)+(3*energy.regen)+(50*(cooldown.tigers_fury.remains<3.5))
        VarEffectiveEnergy = Player:Energy() + (40 * Player:BuffStack(S.Clearcasting)) + (3 * Player:EnergyRegen()) + (50 * num(S.TigersFury:CooldownRemains() < 3.5))
        -- variable,name=time_to_pool,op=set,value=((115-variable.effective_energy-(23*buff.incarnation.up))%energy.regen)
        VarTimeToPool = ((115 - VarEffectiveEnergy - (23 * num(Player:BuffUp(S.Incarnation)))) / Player:EnergyRegen())
        -- variable,name=dot_refresh_soon,value=(!talent.thrashing_claws&(dot.thrash_cat.remains-dot.thrash_cat.duration*0.3<=2))|(talent.lunar_inspiration&(dot.moonfire_cat.remains-dot.moonfire_cat.duration*0.3<=2))|((dot.rake.pmultiplier<1.6|buff.sudden_ambush.up)&(dot.rake.remains-dot.rake.duration*0.3<=2))
        -- TODO: Variable is currently only used in a single 0.2s pool, so we're ignoring it for now.
        -- variable,name=need_bt,value=talent.bloodtalons&buff.bloodtalons.stack<=1
        VarNeedBT = S.Bloodtalons:IsAvailable() and Player:BuffStack(S.BloodtalonsBuff) <= 1
        -- variable,name=cc_capped,value=buff.clearcasting.stack=(1+talent.moment_of_clarity)
        VarCCCapped = Player:BuffStack(S.Clearcasting) == (1 + num(S.MomentofClarity:IsAvailable()))
        -- variable,name=easy_swipe,op=reset
        VarEasySwipe = GetSetting('lazyswipe', true)
    end

    local function Funnel()
        if Target:DebuffUp(S.RipDebuff) then
            if BiteFinisher:IsReady(Target) then
                if Cast(BiteFinisher) then
                    return "Funnel: Ferocious Bite"
                end
            end
        else
            if EnemiesCount8y >= 2 then
                if S.PrimalWrath:IsReady(Target) 
                and not ExcludeNPCListPrimalWrath[Target:NPCID()] then
                    if Cast(S.PrimalWrath) then
                        return "Funnel: Primal Wrath"
                    end
                end
            else
                if S.Rip:IsReady(Target) then
                    if Cast(S.Rip) then
                        return "Funnel: Rip"
                    end
                end
            end
        end
    end

    S.AdaptiveSwarm.TimeSinceLastFlight = 0
    local function APL()
        if not S.AdaptiveSwarm:InFlight() then
            if GetTime() - S.AdaptiveSwarm.TimeSinceLastFlight > 5 then
                S.AdaptiveSwarm.TimeSinceLastFlight = 0
            end
        end

        if S.AdaptiveSwarm:InFlight() then
            if S.AdaptiveSwarm.TimeSinceLastFlight == 0 then
                S.AdaptiveSwarm.TimeSinceLastFlight = GetTime()
            end
            if GetTime() - S.AdaptiveSwarm.TimeSinceLastFlight > 5 then
                S.AdaptiveSwarm.TimeSinceLastFlight = 0
                S.AdaptiveSwarm:RemoveInFlight()
            end
        end

        -- Combo Points
        ComboPoints = Player:ComboPoints()
        ComboPointsDeficit = Player:ComboPointsDeficit()
            
        if TargetIsValid or inCombat then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies8y, false)
            end

            -- Bite Finisher to handle DotC's Ravage
            if Player:HeroTreeID() == 21 then
                BiteFinisher = S.RavageAbilityCat:IsLearned() and S.RavageAbilityCat or S.FerociousBite
            else
                BiteFinisher = S.FerociousBite
            end
        end

        --MainAddon.UpdateVariable("BiteFinisher Name", BiteFinisher:Name())
        --MainAddon.UpdateVariable("BiteFinisher:IsReady()", BiteFinisher:IsReady())
        --MainAddon.UpdateVariable("S.FerociousBite:IsReady()", S.FerociousBite:IsReady())

        if TargetIsValid then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- Precombat
            if not inCombat then
                local ShouldReturn = Precombat();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end

            -- call_action_list,name=variables
            Variables()

            if not inCombat then
                return
            end

            if Target:IsInRange(8) then
                if MainAddon.UsePotion() then
                    MainAddon.SetTopColor(1, "Combat Potion")
                end 
            end

            -- auto_attack,if=!buff.prowl.up|!buff.shadowmeld.up
            -- tigers_fury,if=(energy.deficit>35|combo_points=5|combo_points>=3&dot.rip.refreshable&buff.bloodtalons.up)&(fight_remains<=15|(cooldown.bs_inc.remains>20&target.time_to_die>5)|(cooldown.bs_inc.ready&target.time_to_die>12|target.time_to_die=fight_remains))
            if S.TigersFury:IsReady() and ((Player:EnergyDeficit() > 35 or ComboPoints == 5 or ComboPoints >= 3 and Target:DebuffRefreshable(S.RipDebuff) and Player:BuffUp(S.BloodtalonsBuff)) and (BossFightRemains <= 15 or (BsInc:CooldownRemains() > 20 and Target:TimeToDie() > 5) or (BsInc:CooldownUp() and Target:TimeToDie() > 12 or Target:TimeToDie() == FightRemains))) then
                if Cast(S.TigersFury) then return "tigers_fury main 6"; end
            end
            -- rake,target_if=max:refreshable+(persistent_multiplier>dot.rake.pmultiplier),if=buff.shadowmeld.up|buff.prowl.up
            if S.Rake:IsReady() and (Player:StealthUp(false, true)) then
                if CastTargetIf(S.Rake, EnemiesMelee, "max", EvaluateTargetIfFilterRakeMain, nil) then return "rake main 8"; end
            end
            -- adaptive_swarm,target_if=dot.adaptive_swarm_damage.stack<3&(!dot.adaptive_swarm_damage.ticking|dot.adaptive_swarm_damage.remains<2),if=!action.adaptive_swarm_damage.in_flight&(spell_targets=1|!talent.unbridled_swarm)&(dot.rip.ticking|hero_tree.druid_of_the_claw)
            if S.AdaptiveSwarm:IsReady() and (not S.AdaptiveSwarm:InFlight() and (EnemiesCount8y == 1 or not S.UnbridledSwarm:IsAvailable())) then
                if CastCycle(S.AdaptiveSwarm, Enemies8y, EvaluateCycleAdaptiveSwarm) then return "adaptive_swarm main 14"; end
            end
            -- adaptive_swarm,target_if=max:(1+dot.adaptive_swarm_damage.stack)*dot.adaptive_swarm_damage.stack<3*time_to_die,if=buff.cat_form.up&dot.adaptive_swarm_damage.stack<3&talent.unbridled_swarm.enabled&spell_targets.swipe_cat>1&dot.rip.ticking
            if S.AdaptiveSwarm:IsReady() and (Player:BuffUp(S.CatForm) and S.UnbridledSwarm:IsAvailable() and EnemiesCount8y > 1) then
                if CastTargetIf(S.AdaptiveSwarm, Enemies8y, "max", EvaluateTargetIfFilterAdaptiveSwarm, EvaluateTargetIfAdaptiveSwarm) then return "adaptive_swarm main 16"; end
            end
            -- ferocious_bite,if=buff.apex_predators_craving.up&!(variable.need_bt&active_bt_triggers=2)
            if BiteFinisher:IsReady() and (Player:BuffUp(S.ApexPredatorsCravingBuff) and not (VarNeedBT and CountActiveBtTriggers() == 2)) then
                if Cast(BiteFinisher) then return "ferocious_bite main 18"; end
            end
            -- call_action_list,name=cooldown,if=dot.rip.ticking
            if Target:DebuffUp(S.RipDebuff) then
                local ShouldReturn = Cooldown(); if ShouldReturn then return ShouldReturn; end
            end
            -- rip,if=talent.veinripper&spell_targets=1&hero_tree.wildstalker&!(talent.raging_fury&talent.veinripper)&(buff.bloodtalons.up|!talent.bloodtalons)&(dot.rip.remains<5&buff.tigers_fury.remains>10&combo_points>=3|((buff.tigers_fury.remains<3&combo_points=5)|buff.tigers_fury.remains<=1)&buff.tigers_fury.up&combo_points>=3&remains<cooldown.tigers_fury.remains)
            -- rip,if=!talent.veinripper&spell_targets=1&hero_tree.wildstalker&buff.tigers_fury.up&(buff.bloodtalons.up|!talent.bloodtalons)&(combo_points>=3&refreshable&cooldown.tigers_fury.remains>25|buff.tigers_fury.remains<5&variable.rip_duration>cooldown.tigers_fury.remains&cooldown.tigers_fury.remains>=dot.rip.remains)
            -- Note: Combining both lines.
            if S.Rip:IsReady() and (
            (S.Veinripper:IsAvailable() and EnemiesCount8y == 1 and Player:HeroTreeID() == 22 and not (S.RagingFury:IsAvailable() and S.Veinripper:IsAvailable()) and (Player:BuffUp(S.BloodtalonsBuff) or not S.Bloodtalons:IsAvailable()) and (Target:DebuffRemains(S.RipDebuff) < 5 and Player:BuffRemains(S.TigersFury) > 10 and ComboPoints >= 3 or ((Player:BuffRemains(S.TigersFury) < 3 and ComboPoints == 5) or Player:BuffRemains(S.TigersFury) <= 1) and Player:BuffUp(S.TigersFury) and ComboPoints >= 3 and Target:DebuffRemains(S.RipDebuff) < S.TigersFury:CooldownRemains())) or
            (not S.Veinripper:IsAvailable() and EnemiesCount8y == 1 and Player:HeroTreeID() == 22 and Player:BuffUp(S.TigersFury) and (Player:BuffUp(S.BloodtalonsBuff) or not S.Bloodtalons:IsAvailable()) and (ComboPoints >= 3 and Target:DebuffRefreshable(S.RipDebuff) and S.TigersFury:CooldownRemains() > 25 or Player:BuffRemains(S.TigersFury) < 5 and VarRipDuration > S.TigersFury:CooldownRemains() and S.TigersFury:CooldownRemains() >= Target:DebuffRemains(S.RipDebuff)))
            ) then
                if Cast(S.Rip) then return "rip main 20"; end
            end
            -- call_action_list,name=builder,if=buff.bs_inc.up&!buff.ravage.up&!buff.coiled_to_spring.up&hero_tree.druid_of_the_claw&talent.coiled_to_spring&spell_targets<=2
            if Player:BuffUp(BsInc) and Player:BuffDown(S.RavageBuffFeral) and Player:BuffDown(S.CoiledtoSpringBuff) and Player:HeroTreeID() == 21 and S.CoiledtoSpring:IsAvailable() and EnemiesCount8y <= 2 then
                local ShouldReturn = Builder(); if ShouldReturn then return ShouldReturn; end
            end
            -- wait,sec=action.tigers_fury.ready,if=combo_points=5&cooldown.tigers_fury.remains<3&spell_targets=1
            if ComboPoints == 5 and S.TigersFury:CooldownRemains() < 3 and EnemiesCount8y == 1 then
                if CastPooling(S.Pool, S.TigersFury:CooldownRemains()) then return "wait for tigers_fury"; end
            end
            -- call_action_list,name=finisher,if=combo_points=5
            if ComboPoints == 5 then
                if M.Toggle:GetToggle('Funneling') then
                    local ShouldReturn = Funnel(); if ShouldReturn then return ShouldReturn; end
                else
                    local ShouldReturn = Finisher(); if ShouldReturn then return ShouldReturn; end
                end
            end
            -- call_action_list,name=builder,if=spell_targets.swipe_cat=1&(variable.time_to_pool<=0|!variable.need_bt|variable.proccing_bt)
            if EnemiesCount8y == 1 and (VarTimeToPool <= 0 or not VarNeedBT or VarProccingBT) then
                local ShouldReturn = Builder(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=aoe_builder,if=spell_targets.swipe_cat>=2&combo_points<5&(variable.time_to_pool<=0|!variable.need_bt|variable.proccing_bt)
            if EnemiesCount8y >= 2 and ComboPoints < 5 and (VarTimeToPool <= 0 or not VarNeedBT or VarProccingBT) then
                local ShouldReturn = AoeBuilder(); if ShouldReturn then return ShouldReturn; end
            end
            -- Regrowth filler
            if S.Regrowth:IsReady(Player) and (Player:BuffUp(S.PredatorySwiftnessBuff) and GetSetting('regrowth_pooling', true) and Player:GCDRemains() == 0) then
                if Player:HealthPercentage() < GetSetting('regrowth_self_spin', 30) then
                    if CastAlly(S.Regrowth, Player) then return "regrowth filler - Health"; end
                end

                if GetSetting('regrowth_party_check', false) then
                    local Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()
                    local Reg_Targets = GetSetting('regrowth_targets', {})
                    if HL.Utils.tableCount(Reg_Targets) > 0 then
                        local Reg_Tanks = Reg_Targets['tank']
                        local Reg_Healers = Reg_Targets['healer']
                        local Reg_DPS = Reg_Targets['dps']
    
                        if Reg_Tanks and Tanks then
                            if CastCycleAlly(S.Regrowth, Tanks, EvaluateREG) then
                                return "Regrowth Tanks (Filler)"
                            end
                        end
    
                        if Reg_Healers and Healers then
                            if CastCycleAlly(S.Regrowth, Healers, EvaluateREG) then
                                return "Regrowth Healers (Filler)"
                            end
                        end
    
                        if Reg_DPS and Damagers then
                            if CastCycleAlly(S.Regrowth, Damagers, EvaluateREG) then
                                return "Regrowth DPS (Filler)"
                            end
                        end
                    end
                end

                if Player:HealthPercentage() < 100 or Player:BuffRemains(S.Regrowth) < 5 or Player:BuffRemains(S.PredatorySwiftnessBuff) < 3 then
                    if CastAlly(S.Regrowth, Player) then return "regrowth filler"; end
                end
            end
            -- Pool if nothing else to do
            if (true) then
                if Cast(S.Pool, false, "WAIT") then return "Pool Energy"; end
            end
        end
    end

    local function Main()
        if Player:IsChanneling() then
            return
        end

        -- Custom var update
        inCombat = Player:AffectingCombat()
        TargetIsValid = M.TargetIsValid() 
        inStealth = Player:StealthUp()
        inCat = Player:BuffUp(S.CatForm)
        inDungeon = Player:IsInDungeonArea()
        inRaid = Player:IsInRaidArea()

        -- Update Enemies
        EnemiesMelee = Player:GetEnemiesInMeleeRange(5)
        Enemies8y = Player:GetEnemiesInMeleeRange(8)
        if AoEON() then
            EnemiesCountMelee = #EnemiesMelee
            EnemiesCount8y = #Enemies8y
        else
            EnemiesCountMelee = 1
            EnemiesCount8y = 1
        end

        if M.Toggle:GetToggle('BearMode') then
            if S.BearForm:IsReady(Player) and Player:BuffDown(S.BearForm) then
                if Cast(S.BearForm) then
                    return "Bear Mode: Bear Form"
                end
            end
        end

        -- Utilities
        local ShouldReturn = Utilities();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Defensives
        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- MainAddon.UpdateVariable("Incapacitating Roar", S.IncapacitatingRoar:TimeSinceLastCast())
        -- MainAddon.UpdateVariable("Cat Buff", Player:BuffUp(S.CatForm))
        -- MainAddon.UpdateVariable("Cat Ready", S.CatForm:IsReady(Player))

        if Player.DruidCanShapeShift or S.IncapacitatingRoar:TimeSinceLastCast() < 3 or S.IncapacitatingRoar:TimeSinceLastCast() < 3 then
            if S.CatForm:IsReady(Player) and Player:BuffDown(S.CatForm) then
                if Cast(S.CatForm) then
                    return "Utilities: Cat Form - After roots";
                end
            end
        end

        if Player:BuffUp(S.BearForm) then
            local ShouldReturn = Bear();
            if ShouldReturn then
                return ShouldReturn;
            end
            return
        end

        if Player:BuffDown(S.CatForm) and not S.FluidForm:IsAvailable() then
            return
        end

        if S.FeralFrenzy:TimeSinceLastCast() < Player:GCD() + 1 and Player:ComboPoints() < 5 and Player:PrevGCD(1, S.FeralFrenzy) then
            return
        end

        if not inCombat then
            -- Stealth
            if not Player:IsCasting()
            and not Player:IsInVehicle()
            and (#Player:GetEnemiesInRangeUnfilter(40, "combatimmune") > 0 and GetSetting('autostealth', false) or inRaid or inDungeon or Player:IsInInstancedPvP()) then
                if S.Prowl:IsReady(Player) and not inStealth then
                    if Cast(S.Prowl) then
                        prowlProcessing = true
                        return "Cast Stealth (OOC)"
                    end
                end
            end
        end

        return APL()
    end

    local function OnInit()
        S.RipDebuff:RegisterAuraTracking()
        if M.Toggle:GetToggle('BearMode') then
            MainAddon.Toggle:Switch('BearMode', false)
        end
    end
    M.SetAPL(103, Main, OnInit)

    local regrowthProcessing = false
    HL:RegisterForEvent(
        function(arg1, arg2, arg3, arg4, arg5)
            if arg2 == "player" then
                if arg5 == S.Regrowth:ID() then
                    regrowthProcessing = true
                    C_Timer.After(1, function()
                        regrowthProcessing = false
                    end)
                end

                -- Symbiotic Relationship blacklist to prevent spam
                if arg5 == 474750 then
                    TempBlackListSymbioticRelationship[arg3] = true
                    C_Timer.After(S.SymbioticRelationship:CastTime() + (HL.Latency()*7), function()
                        TempBlackListSymbioticRelationship[arg3] = nil
                    end)
                end
            end
        end, 
    "UNIT_SPELLCAST_SENT")

    
    HL:RegisterForSelfCombatEvent(
        function(_, _, _, _, _, _, _, _, _, _, _, SpellID)
            if SpellID == 102547 or SpellID == 5215 then
                prowlProcessing = false
            end
        end,
    "SPELL_AURA_REMOVED")

    local FeralOldSpellIsReady
    FeralOldSpellIsReady = HL.AddCoreOverride("Spell.IsReady",
            function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                if MainAddon.PlayerSpecID() == 103 then
                    if M.Toggle:GetToggle('BearMode') then
                        if self == S.CatForm then
                            return false, "Bear Mode"
                        end
                    end
                end
                if GetSetting('cdnodispel', true) then
                    if (self == S.IncapacitatingRoar) or (self == S.RemoveCorruption) then
                        if Player:BuffUp(S.Berserk) or Player:BuffUp(S.Incarnation) then
                            return false, "Disabled due to CDs"
                        end
                    end
                end
                local BaseCheck, Reason = FeralOldSpellIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                return BaseCheck, Reason
            end
    , 103)

    local OldCooldownRemains
    OldCooldownRemains = HL.AddCoreOverride("Spell.CooldownRemains",
            function(self, BypassRecovery, BypassCD)
                local BaseCheck = OldCooldownRemains(self, BypassRecovery, BypassCD)
                if MainAddon.PlayerSpecID() == 103 then
                    if self == S.SkullBash then
                        if not inCat then
                            return 99999, 'Not in Catform'
                        end
                    end

                    if self == S.CatForm then
                        if not S.CatForm:IsBlocked() then
                            return 0
                        end
                    end

                    if self == S.BearForm then
                        if not S.BearForm:IsBlocked() then
                            return 0
                        end
                    end
                end
                return BaseCheck
            end
    , 103)

    local OldMaximumRangeX
    OldMaximumRangeX = HL.AddCoreOverride("Spell.MaximumRangeX",
            function(self)
                local BaseCheck = OldMaximumRangeX(self)
                if MainAddon.PlayerSpecID() == 103 then
                    local feralrangekick = GetSetting('feralrangekick', 5)

                    if self == S.SkullBash then
                        if feralrangekick <= 5 then
                            return 5
                        end
                        if feralrangekick >= 16 then
                            return 16
                        end
                        return feralrangekick
                    end
                end
                return BaseCheck
            end
    , 103)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 103 then
                    if inStealth then
                        if self == S.AdaptiveSwarm then
                            return false, "Stealth"
                        end

                        if self == S.MarkoftheWild then
                            return false, "Stealth"
                        end
                    end

                    -- Have to do this to prevent weird Thrash/Brutal Slash/Swipe behavior since they don't require any target
                    -- Also prevent those spells from being used if player is not in melee range of target.
                    if self == S.ThrashCat or self == S.BrutalSlash or self == S.Swipe then
                        if inStealth or not inCombat or not Target:IsSpellInRange(S.Shred) then
                            return false, "Stealth/OOC/Out of Range"
                        end
                    end

                    if self == S.ConvoketheSpirits then
                        if GetSetting("Align2Min", false) and not Player:BuffUp(BsInc) then
                            return false, "Convoke the Spirits under Berserk"
                        end
                    end

                    if self == S.Regrowth then
                        if regrowthProcessing then
                            return false, "Regrowth is processing"
                        end
                    end

                    if inCombat then
                        -- Avoid combat Prowl 
                        if self == S.Prowl then
                            -- in Solo content.
                            if not Player:IsInPartyOrRaid() and not Target:IsDummy() then
                                return false, "In solo content."
                            end
                        end

                        -- Avoid Shadowmeld
                        if self == S.Shadowmeld then
                            -- if in Solo content.
                            if not Player:IsInPartyOrRaid() and not Target:IsDummy() then
                                return false, "In solo content."
                            end
                            -- if Prowl is processing.
                            if prowlProcessing then
                                return false, "Prowl is processing."
                            end
                        end
                    end
                end
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 103);

    local function mod_circle_dot(x)
        return x * (num(S.CircleofLifeandDeath:IsAvailable()) and 0.8 or 1)
    end

    local FeralOldSpellBaseDuration
    FeralOldSpellBaseDuration = HL.AddCoreOverride("Spell.BaseDuration",
            function(self)
                if MainAddon.PlayerSpecID() == 103 then
                    if self:ID() == 405233 or self:ID() == 1822 then
                        return ((1 * 15) * Player:SpellHaste()) * 1
                    end

                    if self:ID() == 1079 then
                        return mod_circle_dot(1 * (4 + (ComboPoints * 4)))
                    end
                end

                local BaseCheck = FeralOldSpellBaseDuration(self)
                return BaseCheck
            end
    , 103);
end